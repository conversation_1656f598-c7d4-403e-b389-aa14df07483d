
package Lib::PrepFlows;

use lib "/opt/apache/app/";


use Exporter;
use Text::CSV;
use Text::CSV_XS;

use Lib::DataSources;
use Lib::DSRCreateUpdate;
use Lib::DSRUtils;
use Lib::PrepClientTrim;
use Lib::PrepSources;
use Lib::PrepUtils;
use Lib::WebUtils;

our @ISA = ('Exporter');

our @EXPORT = qw(
    &prep_audit
    &flow_telemetry
    &prep_flow_rights
    &prep_flow_list
    &prep_flow_id_to_name
    &prep_flow_get_column_hash
    &prep_flow_get_name_hash
    &prep_flow_get_owner_hash
    &prep_flow_order_columns
    &prep_flow_available_job_slot
    &prep_flow_storage
    &prep_flow_create_job
    &prep_flow_create_interactive_job
    &prep_flow_expand_zips
    &prep_flow_extract_file_data
    &prep_flow_nested_to_tabular
    &prep_flow_load_raw_data
    &prep_flow_set_file_types
    &prep_flow_merge_data
    &prep_flow_detect_column_type_by_name
    &prep_flow_detect_column_types
    &prep_flow_validate
    &prep_flow_export_koala
    &prep_run_flow
    &PrepFlows_analyze_job_dim_details
    &prep_flow_update_schedule_status
    &prep_job_clear
    &prep_running_jobs
    &prep_autoscale_number
    &prep_web_cloud_dashboard
    &prep_flow_test_uploaded_files
    &prep_flow_send_tested_files_to_manager
  );


#-------------------------------------------------------------------------
#
# Log telemetry information about the data flow run
#

sub flow_telemetry
{
  my ($query, $q_text);

  my ($prepDB, $jobID, $text) = @_;


  if ($jobID < 1)
  {
    return;
  }

  $text = ": $text\n";
  $q_text = $prepDB->quote($text);
  $query = "UPDATE prep.telemetry
      SET telemetry = CONCAT(telemetry, NOW(), $q_text) WHERE jobID=$jobID";
  $prepDB->do($query);
}



#-------------------------------------------------------------------------
#
# Add an entry to the data prep audit log.
#

sub prep_audit
{
  my ($query, $q_action, $status);

  my ($prepDB, $userID, $action, $flowID) = @_;


  #correctly handle cases where data fields don't make sense to store
  if (length($userID) < 1)
  {
    $userID = "NULL";
  }
  if ($flowID == 0)
  {
    $flowID = "NULL";
  }

  if (length($action) < 1)
  {
    $action = "Whoa! Got an empty action!";
  }

  #if the action string is too long to fit in its column...
  if (length($action) > 127)
  {
    $action = substr($action, 0, 124);
    $action = $action . "...";
  }

  $q_action = $prepDB->quote($action);

  $query = "INSERT INTO prep.audit (userID, timestamp, action, flowID)
      VALUES ($userID, NOW(), $q_action, $flowID)";

  # Add exception handling for the SQL statement
  eval {
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  };
  if ($@) {
    my $date = localtime();
    print STDERR "$date: Exception in prep_audit: $@\n";
    print STDERR "$date: Failed query: $query\n";

    # Send email notification for audit function exception
    PrepUtils_send_exception_email("Audit Function Error", $@, $query, "PrepFlows::prep_audit");
  }
}



#-------------------------------------------------------------------------
#
# Return an array of data prep flow IDs that the specified user has some
# level of privileges on (used mostly to display list of data flows in UI).
#

sub prep_flow_list
{
  my ($query, $dbOutput, $status, $flowID, $ownerID);
  my ($Rusers, $RWusers, $ruser);
  my (@rusers, @rwusers, @userFlows);
  my (%seen);

  my ($prepDB, $kapDB, $userID, $acctType) = @_;


  #data sources owned by the user are first on the list
  $query = "SELECT ID FROM prep.flows WHERE userID=$userID ORDER BY name";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  while (($flowID) = $dbOutput->fetchrow_array)
  {
    push(@userFlows, $flowID);
  }

  #get the list of data flows stored on the system, with priv info
  $query = "SELECT ID, userID, Rusers, RWusers FROM prep.flows ORDER BY name";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #run through the list of data sources
  while (($flowID, $ownerID, $Rusers, $RWusers) = $dbOutput->fetchrow_array)
  {

    #split the user lists into arrays, and combine
    @rusers = split(',', $Rusers);
    @rwusers = split(',', $RWusers);
    push(@rusers, @rwusers);

    #push the data source owner's ID onto the combined array
    push(@rusers, $ownerID);

    #see if the user has privs, and add the data source ID to the returned
    #array if so
    foreach $ruser (@rusers)
    {
      if (($ruser == $userID) || ($acctType > 4))
      {
        push(@userFlows, $flowID);
      }
    }
  }

  #unique-ify the list of data flows
  @userFlows = grep { !$seen{$_}++ } @userFlows;

  return(@userFlows);
}



#-------------------------------------------------------------------------
#
# Determine what rights the specified user has to the specified data flow.
# Returns a single character: "N" for no rights, "R" for run-only, "W" for
# write (edit).
#

sub prep_flow_rights
{
  my ($userEmail, $query, $dbOutput, $status, $ownerID, $Rusers, $RWusers);
  my ($ruser, $rwuser);
  my (@rusers, @rwusers);

  my ($prepDB, $kapDB, $userID, $flowID, $acctType) = @_;


  #admins can access everything
  if ($acctType > 4)
  {
    return("W");
  }

  #get the list of read and read/write users for the specified data source
  $query = "SELECT userID, Rusers, RWusers FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($ownerID, $Rusers, $RWusers) = $dbOutput->fetchrow_array;

  #if the user owns the data source, they have full rights
  if ($userID == $ownerID)
  {
    return("W");
  }

  #split the user lists into arrays
  @rusers = split(',', $Rusers);
  @rwusers = split(',', $RWusers);

  #see if the user has read/write privs
  foreach $rwuser (@rwusers)
  {
    if ($rwuser == $userID)
    {
      return("W");
    }
  }

  #see if the user has run privs
  foreach $ruser (@rusers)
  {
    if ($ruser == $userID)
    {
      return("R");
    }
  }

  #if we made it this far, the user has no privs on the data source
  return("N");
}



#-------------------------------------------------------------------------
#
# Build and return a hash of all data flows, hashed by ID
#

sub prep_flow_get_name_hash
{
  my ($query, $dbOutput, $status, $id, $name);
  my (%flowNames);

  my ($prepDB) = @_;


  undef(%flowNames);

  #add user names to hash
  $query = "SELECT ID, name FROM prep.flows";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $flowNames{$id} = $name;
  }

  return(%flowNames);
}



#-------------------------------------------------------------------------
#
# Build and return a hash of the owner of all data flows, hashed by ID
#

sub prep_flow_get_owner_hash
{
  my ($query, $dbOutput, $status, $id, $ownerID);
  my (%flowOwners);

  my ($prepDB) = @_;


  #add user names to hash
  $query = "SELECT ID, userID FROM prep.flows";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  while (($id, $ownerID) = $dbOutput->fetchrow_array)
  {
    $flowOwners{$id} = $ownerID;
  }

  return(%flowOwners);
}



#-------------------------------------------------------------------------
#
# Return the text name of the data flow with the supplied ID
#

sub prep_flow_id_to_name
{
  my ($query, $dbOutput, $status, $name);

  my ($prepDB, $flowID) = @_;


  $query = "SELECT name FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($name) = $dbOutput->fetchrow_array;

  return($name);
}



#-------------------------------------------------------------------------
#
# Get a hash of column IDs for the specified job
#

sub prep_flow_get_column_hash
{
  my ($masterColTable, $query, $dbOutput, $status, $id, $name);
  my (%columnNames);

  my ($prepDB, $flowID, $jobID) = @_;


  undef(%columnNames);

  #add column names to hash
  $masterColTable = "prep_data.$jobID" . "_master_cols";
  $query = "SELECT ID, name FROM $masterColTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  while (($id, $name) = $dbOutput->fetchrow_array)
  {
    $columnNames{$id} = $name;
  }

  return(%columnNames);
}



#-------------------------------------------------------------------------
#
# Return an array of master table column IDs, arranged in human-logical display
# order
#

sub prep_flow_order_columns
{
  my ($masterColTable, $query, $dbOutput, $id, $type, $status);
  my (@columnIDs, @tmp);
  my (%typeHash);

  my ($prepDB, $flowID, $jobID) = @_;


  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #build a hash of data columns, keyed by type
  $query = "SELECT ID, type FROM $masterColTable ORDER BY name";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($id, $type) = $dbOutput->fetchrow_array)
  {
    $typeHash{$type} = $typeHash{$type} . "$id,";
  }

  undef(@columnIDs);

  #product columns go first
  @tmp = split(',', $typeHash{'product'});
  push(@columnIDs, @tmp);

  #product aliases
  @tmp = split(',', $typeHash{'palias'});
  push(@columnIDs, @tmp);

  #geographies
  @tmp = split(',', $typeHash{'geography'});
  push(@columnIDs, @tmp);

  #geography aliases
  @tmp = split(',', $typeHash{'galias'});
  push(@columnIDs, @tmp);

  #time periods
  @tmp = split(',', $typeHash{'time'});
  push(@columnIDs, @tmp);

  #time aliases
  @tmp = split(',', $typeHash{'talias'});
  push(@columnIDs, @tmp);

  #UPC/SKU
  @tmp = split(',', $typeHash{'upc'});
  push(@columnIDs, @tmp);

  #product attributes
  @tmp = split(',', $typeHash{'pattr'});
  push(@columnIDs, @tmp);

  #geography attributes
  @tmp = split(',', $typeHash{'gattr'});
  push(@columnIDs, @tmp);

  #time attributes
  @tmp = split(',', $typeHash{'tattr'});
  push(@columnIDs, @tmp);

  #product segments
  @tmp = split(',', $typeHash{'pseg'});
  push(@columnIDs, @tmp);

  #geography segments
  @tmp = split(',', $typeHash{'gseg'});
  push(@columnIDs, @tmp);

  #time segments
  @tmp = split(',', $typeHash{'tseg'});
  push(@columnIDs, @tmp);

  #measures
  @tmp = split(',', $typeHash{'measure'});
  push(@columnIDs, @tmp);

  return(@columnIDs);
}



#-------------------------------------------------------------------------
#
# Determine if there's an available slot for a job to run - if not, the
# calling process should go into a wait state.
#

sub prep_flow_available_job_slot
{
  my ($runningJobs, $loadRatio, $userEmail, $query, $status, $dbOutput);
  my ($userJobs);

  my ($prepDB, $kapDB, $jobID, $userID) = @_;


  #background agents always get to run, no matter what
  if ($userID == 0)
  {
    return(1);
  }

  #get the number of running jobs on the cloud and calc the load ratio
  $runningJobs = prep_running_jobs($prepDB);
  $loadRatio = $runningJobs / $Lib::KoalaConfig::prepCores;

  #if the cloud is less than 75% loaded, let the job process run no matter what
  if ($loadRatio < 0.75)
  {
    return(1);
  }

  #NB: everything below here means the cloud is running at 75% or higher

  #if the user is super admin, let their process run regardless of load
  $userEmail = utils_userID_to_email($kapDB, $userID);
  if ($userEmail eq "support\@koala-corp.com")
  {
    return(1);
  }

  #if the load is less than 100% and the user has less than 2 running jobs,
  #let their job run
  if ($loadRatio < 1)
  {
    $query  = "SELECT COUNT(*) FROM prep.jobs
        WHERE state != 'LOADED' AND userID=$userID AND PID != $$";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($userJobs) = $dbOutput->fetchrow_array;

    if ($userJobs < 2)
    {
      returln(1);
    }
  }

  #the load is over 100% and/or the user already has at least 3 jobs, so
  #put them in the wait queue
  return(0);
}



#-------------------------------------------------------------------------
#
# Return the current level of storage usage on the Data Prep volume. The
# mount point is specified in KoalaConfig.pm - if not, we fall back to using
# the master Koala mount point.
#

sub prep_flow_storage
{
  my ($storagePct, $mountPoint, $output);

  my ($acctType) = @_;


  #assume a super-admin knows what they're doing
  if ($acctType > 5)
  {
    return(1);
  }

  #figure out our data volume's mountpoint (if we can't, allow the op to go)
  $mountPoint = $Lib::KoalaConfig::prepDataDisk;
  if (length($mountPoint) < 1)
  {
    $mountPoint = $Lib::KoalaConfig::dataDisk;
  }
  if (length($mountPoint) < 0)
  {
    return(1);
  }

  $output = `df -h | grep $mountPoint`;
  $output =~ m/^.*\s+(\d+)%\s+/;
  $storagePct = $1;
  $storagePct += 15;
  return($storagePct);
}



#-------------------------------------------------------------------------
#
# Do the setup work for a new job (mostly creating database tables for it).
#

sub prep_flow_create_job
{
  my ($query, $status, $jobID, $ownerID);

  my ($prepDB, $flowID, $userID) = @_;


  $ownerID = PrepUtils_get_flow_owner($prepDB, $flowID);

  #insert our job entry into the database and get back our job ID
  $query = "INSERT INTO prep.jobs (flowID, userID, ownerID, mode, lastAction, opInfo)
      VALUES ($flowID, $userID, $ownerID, 'run', NOW(), 'Running Data Flow')";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
  $jobID = $prepDB->{q{mysql_insertid}};

  #update the last run status for the flow
  $query = "UPDATE prep.flows SET lastRun=NOW() WHERE ID=$flowID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #insert the telemetry entry for our job
  $query = "INSERT INTO prep.telemetry (jobID, flowID, startTime, telemetry)
      VALUES ($jobID, $flowID, NOW(), CONCAT(NOW(), ': Starting run of data flow\n'))";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
  prep_audit($prepDB, $userID, "Ran data flow|$jobID", $flowID);

  #insert the detailed history entry for our job
  $query = "INSERT INTO prep.job_history (jobID, flowID, userID, ownerID, status)
      VALUES($jobID, $flowID, $userID, $ownerID, 'running')";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  PrepUtils_store_scaled_cloud_load($prepDB, $jobID);

  return($jobID);
}



#-------------------------------------------------------------------------
#
# Do the setup work for a new interactive job.
#

sub prep_flow_create_interactive_job
{
  my ($query, $dbOutput, $status, $jobID, $ownerID);

  my ($prepDB, $flowID, $userID, $state) = @_;


  $ownerID = PrepUtils_get_flow_owner($prepDB, $flowID);

  #insert our job entry into the database and get back our job ID
  $query = "INSERT INTO prep.jobs (flowID, userID, ownerID, mode, lastAction, opInfo, state)
      VALUES ($flowID, $userID, $ownerID, 'interactive', NOW(), 'Downloading data', '$state')";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
  $jobID = $prepDB->{q{mysql_insertid}};

  #update the last run status for the flow
  $query = "UPDATE prep.flows SET lastRun=NOW() WHERE ID=$flowID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #insert the telemetry entry for our job
  $query = "INSERT INTO prep.telemetry (jobID, flowID, startTime, telemetry)
      VALUES ($jobID, $flowID, NOW(), CONCAT(NOW(), ': Creating job\n'))";
  $prepDB->do($query);
  prep_audit($prepDB, $userID, "Ran data flow|$jobID", $flowID);

  #insert the detailed history entry for our job
  $query = "INSERT INTO prep.job_history (jobID, flowID, userID, ownerID, status)
      VALUES($jobID, $flowID, $userID, $ownerID, 'running')";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  PrepUtils_store_scaled_cloud_load($prepDB, $jobID);

  return($jobID);
}



#-------------------------------------------------------------------------
#
# Expand any zip archives that might have been uploaded as part of the
# current run, making sure to name any extracted files with the current
# job's key so it'll be picked up.
#

sub prep_flow_expand_zips
{
  my ($query, $filename, $tmpDirName, $prepend, $zipfilename, $oldName, $state);
  my ($dbOutput, $newName, $filesProcessed, $totalFiles, $pct, $userFileName);
  my ($opInfo, $q_opInfo, $tmpDirName, $status);
  my (@zipFiles);

  my ($prepDB, $jobID, $userID, $key) = @_;


  flow_telemetry($prepDB, $jobID, "Starting expansion of compressed archives");
  PrepUtils_store_scaled_cloud_load($prepDB, $jobID);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Expanding compressed files");

  #check the job's state to make sure it's OK for us to run (should be after
  #a "LOAD-*" operation)
  $query = "SELECT opInfo, state FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($opInfo, $state) = $dbOutput->fetchrow_array;
  if ((!($state =~ m/^LOAD-/)) && (!($key !=~ m/^M\d+/)))
  {
    flow_telemetry($prepDB, $jobID, "WARNING: expand_zips called out of sequence: $state");
    return;
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set initial status
  $opInfo = "0|Uncompressing data";
  $q_opInfo = $prepDB->quote($opInfo);
  $query = "UPDATE prep.jobs SET PID=$$, opInfo=$q_opInfo, state='EXTRACT-DATA'
      WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #open the directory, and scan it for files that will be processed by this job
  #and also collect zip files in a single pass
  opendir(DIRHANDLE, "/opt/apache/app/tmp");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    # Check if this is a file for this job
    if ($filename =~  m/^prep\.$userID\.$key\.(.*)$/i)
    {
      $q_filename = $prepDB->quote("$1|");
      $query = "UPDATE prep.job_history
          SET filenames = CONCAT(IFNULL(filenames, ''), $q_filename) WHERE jobID=$jobID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);

      # Also check if it's a zip file that needs to be expanded
      if (($filename =~  m/^prep\.$userID\.$key\.(.*)\.zip$/i) ||
          ($filename =~  m/^prep\.$userID\.$key\.(.*)\.gz$/i))
      {
        push(@zipFiles, $filename);
        flow_telemetry($prepDB, $jobID, "Found $filename that needs to be expanded");
      }
    }
  }

  $filesProcessed = 0;
  $totalFiles = scalar(@zipFiles);
  foreach $filename (@zipFiles)
  {

    #update detailed status info
    $pct = ($filesProcessed / $totalFiles) * 100;
    $pct = int($pct) - 1;
    if ($pct < 0)
    {
      $pct = 0;
    }
    PrepUtils_set_job_op_pct($prepDB, $jobID, "$pct");

    $userFileName = $filename;
    if ($filename =~ m/^prep\.\d+\..*?\.(.*)$/)
    {
      $userFileName = $1;
    }

    PrepUtils_set_job_op_details($prepDB, $jobID, "Uncompressing <TT>$userFileName</TT>");

    $opInfo = "$pct|Uncompressing $userFileName";
    $q_opInfo = $prepDB->quote($opInfo);
    $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
    $prepDB->do($query);

    #create a temp directory to hold the files from the zip archive
    $tmpDirName = "/opt/apache/app/tmp/prep.$userID.$key";
    mkdir($tmpDirName);

    if ($filename =~ m/\.gz$/)
    {
      flow_telemetry($prepDB, $jobID, "Using gzip to expand $userFileName");
      `mv \"/opt/apache/app/tmp/$filename\" $tmpDirName`;
      `gzip -d \"$tmpDirName/$filename\"`;
    }
    else
    {
      flow_telemetry($prepDB, $jobID, "Using unzip to expand $userFileName");

      #extract all of the files from the zip into that directory
      #NB: the -j qualifier tells unzip to ignore any dir structure in archive
      `unzip -j \"/opt/apache/app/tmp/$filename\" -d $tmpDirName`;
    }

    #prepend all of the files from the zip with our run key, then move up
    #to the main tmp working directory
    $prepend = "prep.$userID.$key";
    opendir(ZIPHANDLE, $tmpDirName);
    while (defined($zipfilename = readdir(ZIPHANDLE)))
    {
      $oldName = "$tmpDirName/$zipfilename";
      $newName = "/opt/apache/app/tmp/$prepend.$zipfilename";
      rename($oldName, $newName);
    }
    closedir(ZIPHANDLE);

    #remove the temp directory
    rmdir($tmpDirName);

    #remove the zip archive
    unlink("/opt/apache/app/tmp/$filename");
  }

  closedir(DIRHANDLE);

  #NB: we don't clear all of the detailed op stats here because we're called as
  #   part of the overall extract data operation
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  PrepUtils_set_job_op_speed($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  flow_telemetry($prepDB, $jobID, "Done expanding compressed archives");

  $query = "UPDATE prep.jobs SET PID=NULL, opInfo='99|Uncompressing Files', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_pct($prepDB, $jobID, "99");
}



#-------------------------------------------------------------------------
#
# Run through every uploaded file associated with the current job,
# preforming any necessary data extraction and adding them to the files
# database.
#

sub prep_flow_extract_file_data
{
  my ($DIRHANDLE, $filename, $filesProcessed, $totalFiles, $userFilename);
  my ($pct, $opInfo, $q_opInfo, $query, $q_userFilename, $q_filename);
  my ($outputFile, $sheetID, $q_tabName, $workbookXML, $sheetName);
  my ($dbOutput, $runningJobs, $state, $status);
  my (@sourceFiles);
  my (%tabHash);

  my ($prepDB, $kapDB, $flowID, $jobID, $userID, $key) = @_;


  flow_telemetry($prepDB, $jobID, "Starting data extraction from files");

  #check the job's state to make sure it's OK for us to run (should be after
  #the expand zips (EXTRACT-DATA) operation)
  $query = "SELECT opInfo, state FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($opInfo, $state) = $dbOutput->fetchrow_array;
  if ($state ne "EXTRACT-DATA")
  {
    flow_telemetry($prepDB, $jobID, "WARNING: extract_file_data called out of sequence: $state");
    return;
  }

  PrepUtils_store_scaled_cloud_load($prepDB, $jobID);

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Extracting data from files");

  #if the system is fully loaded, back off and wait for an open process slot
  $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);

  # Set a maximum wait time (30 minutes = 15 attempts at 120 seconds each)
  my $maxWaitAttempts = 15;
  my $waitAttempts = 0;

  while (!$okToRun && $waitAttempts < $maxWaitAttempts)
  {
    $query = "UPDATE prep.jobs
        SET PID=$$, opInfo='0| |Waiting in processing queue', state='EXTRACT-DATA-WAIT'
        WHERE ID=$jobID";
    $prepDB->do($query);
    PrepUtils_set_job_op_details($prepDB, $jobID, "Waiting in processing queue (attempt $waitAttempts of $maxWaitAttempts)");

    sleep(120);
    $waitAttempts++;

    $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
  }

  # If we've reached the maximum wait time and still can't run, log it and continue anyway
  if (!$okToRun)
  {
    flow_telemetry($prepDB, $jobID, "WARNING: Maximum wait time exceeded, proceeding with extraction despite system load");
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set initial status
  $opInfo = "0|Extracting data";
  $q_opInfo = $prepDB->quote($opInfo);
  $query = "UPDATE prep.jobs SET PID=$$, opInfo=$q_opInfo, state='EXTRACT-DATA'
      WHERE ID=$jobID";
  $prepDB->do($query);

  opendir(DIRHANDLE, "/opt/apache/app/tmp");

  #open the directory, and scan it for data files uploaded by our user
  opendir(DIRHANDLE, "/opt/apache/app/tmp");
  while (defined($filename = readdir(DIRHANDLE)))
  {
    if ($filename =~  m/^prep\.$userID\.$key\..*/)
    {
      push(@sourceFiles, $filename);
    }
  }

  $filesProcessed = 0;
  $totalFiles = scalar(@sourceFiles);
  foreach $filename (@sourceFiles)
  {
    $filename =~ m/^prep\.$userID\.$key\.(.*)/;
    $userFilename = $1;

    #update detailed status info
    $pct = ($filesProcessed / $totalFiles) * 100;
    $pct = int($pct);
    $opInfo = "$pct|Extracting data from $userFilename";
    $q_opInfo = $prepDB->quote($opInfo);
    $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
    $prepDB->do($query);

    flow_telemetry($prepDB, $jobID, "Extracting data from $userFilename");

    PrepUtils_set_job_op_details($prepDB, $jobID, "Extracting data from <TT>$userFilename</TT>");
    PrepUtils_set_job_op_pct($prepDB, $jobID, $pct);

    #if we're dealing with a file that's already a CSV, TSV, or TXT file
    if (($filename =~ m/\.csv$/i) || ($filename =~ m/\.tsv$/i) ||
        ($filename =~ m/\.txt$/i))
    {

      #nothing to do but add file to the database for this job
      $q_userFilename = $prepDB->quote($userFilename);
      $q_filename = $prepDB->quote($filename);
      $query = "INSERT INTO prep.files (jobID, flowID, userFilename, filename)
          VALUES ($jobID, $flowID, $q_userFilename, $q_filename)";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);

      $fileSize = -s "/opt/apache/app/tmp/$filename";
      $query = "UPDATE prep.job_history
          SET dataProcessed = dataProcessed + $fileSize WHERE jobID=$jobID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }

    #if we're dealing with an XLS file
    if ($filename =~ m/\.xls$/i)
    {
      $outputFile = "$filename.csv";
      $sheetID = 1;
      `/usr/bin/xls2csv \"/opt/apache/app/tmp/$filename\" -o \"/opt/apache/app/tmp/$outputFile\"`;
      `rm /opt/apache/app/tmp/\"$filename\"`;

      #add file info to files table in prep database
      $q_userFilename = $prepDB->quote($userFilename);
      $q_tabName = $prepDB->quote("Tab 1");
      $q_filename = $prepDB->quote($outputFile);
      $query = "INSERT INTO prep.files (jobID, flowID, userFilename, filename, tabName, tabID)
          VALUES ($jobID, $flowID, $q_userFilename, $q_filename, $q_tabName, $sheetID)";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);

      $fileSize = -s "/opt/apache/app/tmp/$outputFile";
      $query = "UPDATE prep.job_history
          SET dataProcessed = dataProcessed + $fileSize WHERE jobID=$jobID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }

    #if we're dealing with an XLSX file
    if (($filename =~ m/\.xlsx$/i) || ($filename =~ m/\.xlsm$/i))
    {

      #extract the XML file containing list of tabs from the XLSX
      $workbookXML = `unzip -p \"/opt/apache/app/tmp/$filename\" \"xl/workbook.xml\"`;

      #build up a hash of each sheet ID and name in the workbook
      undef(%tabHash);
      while ($workbookXML =~ m/<sheet name=\"(.*?)\" .*? r:id=\"rId(\d+)\".*?\/>/g)
      {
        $sheetName = $1;
        $sheetID = $2;

        #don't extract IRI's internal app info sheet
        if ($sheetName =~ m/IRI_WorkspaceStorage/)
        {
          next;
        }

        #don't extract Nielsen AOD's internal app info sheet
        if ($sheetName =~ m/ReportMeta/)
        {
          next;
        }
        if ($sheetName =~ m/TOC/)
        {
          next;
        }
        $tabHash{$sheetID} = $sheetName;
      }

      #extract the data from each tab, and add to files table
      foreach $sheetID (keys %tabHash)
      {
        flow_telemetry($prepDB, $jobID, "--- Extracting data from tab $tabHash{$sheetID}");

        #do the actual extraction of the raw data
        $outputFile = "$filename-$sheetID.csv";
        `/usr/bin/python /opt/xlsx2csv/xlsx2csv.py -e -s $sheetID -i \"/opt/apache/app/tmp/$filename\" \"/opt/apache/app/tmp/$outputFile\"`;

        #add file info to files table in prep database
        $q_userFilename = $prepDB->quote($userFilename);
        $q_tabName = $prepDB->quote($tabHash{$sheetID});
        $q_filename = $prepDB->quote($outputFile);
        $query = "INSERT INTO prep.files (jobID, flowID, userFilename, filename, tabName, tabID)
            VALUES ($jobID, $flowID, $q_userFilename, $q_filename, $q_tabName, $sheetID)";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);

        $fileSize = -s "/opt/apache/app/tmp/$outputFile";
        $query = "UPDATE prep.job_history
            SET dataProcessed = dataProcessed + $fileSize WHERE jobID=$jobID";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
      }

      `rm /opt/apache/app/tmp/\"$filename\"`
    }

    $filesProcessed++;
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  PrepUtils_set_job_op_speed($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  flow_telemetry($prepDB, $jobID, "Done extracting data from files");

  $query = "UPDATE prep.jobs SET PID=NULL, opInfo='DONE', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);
}



#-------------------------------------------------------------------------
#
# Convert a nested table-formatted file that follows the IRIv1 layout into
# a tabular file.
#

sub prep_nested_iriv1_to_csv
{
  my ($csv, $line, $time, $geog, $prod, $dimData);
  my ($INPUT, $OUTPUT);
  my (@columns, @outputCols);

  my ($prepDB, $filename) = @_;


  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  #open the input CSV file for reading
  open(INPUT, "/opt/apache/app/tmp/$filename");

  #open our output CSV file
  open(OUTPUT, ">/opt/apache/app/tmp/$filename.1");

  #read the first line of the file, containing measure names
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  #shift off the first (empty) field from the source file
  shift(@columns);

  #build a tabular header line, and write it out
  $outputCols[0] = "Geog";
  $outputCols[1] = "Time";
  $outputCols[2] = "Prod";
  push(@outputCols, @columns);
  $csv->combine(@outputCols);
  $line = $csv->string();
  print OUTPUT "$line\n";

  #cycle through the data in the source file, converting to tabular
  while ($line = <INPUT>)
  {
    #parse the CSV line we just took in, and convert to array
    $csv->parse($line);
    @columns = $csv->fields();

    #if the line contains dimension info (it only contains one item)
    if (length($columns[1]) < 1)
    {

      #don't know if this is a geog or time yet, but it's one of the two
      $dimData = $columns[0];

      #read the next line (contains dimension info, or measure data)
      $line = <INPUT>;
      $csv->parse($line);
      @columns = $csv->fields();

      #if the second line also contains dimension info (only one item)
      if (length($columns[1]) < 1)
      {
        #the first piece of dimension info we read was a geog
        $geog = $dimData;

        #the second piece was a time period
        $time = $columns[0];

        #and we'll read/parse the next line of data, and continue processing
        $line = <INPUT>;
        $csv->parse($line);
        @columns = $csv->fields();
      }

      #else if the next line contains data, so we only got a time period
      else
      {
        $time = $dimData;

        #NB: we've already read and parsed the next line of data
      }
    }

    #convert line of data to tabular format
    undef(@outputCols);
    $outputCols[0] = $geog;
    $outputCols[1] = $time;
    push(@outputCols, @columns);

    #convert array to CSV, and write to output file
    $csv->combine(@outputCols);
    $line = $csv->string();
    print OUTPUT "$line\n";
  }

  #close and flush out the input/output files
  close(INPUT);
  close(OUTPUT);

  #unlink the source file
  unlink("/opt/apache/app/tmp/$filename");

  #rename the output file to the source file's name
  rename("/opt/apache/app/tmp/$filename.1", "/opt/apache/app/tmp/$filename");
}



#-------------------------------------------------------------------------
#
# Convert a nested table-formatted file that follows the IRIv2 layout into
# a tabular file.
#

sub prep_nested_iriv2_to_csv
{
  my ($csv, $line, $time, $geog, $prod);
  my (@columns, @outputCols);
  my ($INPUT, $OUTPUT);

  my ($prepDB, $filename) = @_;


  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  #open the input CSV file for reading
  open(INPUT, "/opt/apache/app/tmp/$filename");

  #open our output CSV file
  open(OUTPUT, ">/opt/apache/app/tmp/$filename.1");

  #burn the first line of the file, contains "Analyzer Report" string
  $line = <INPUT>;

  #read the second line of the file, contains geography as first field
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();
  $geog = $columns[0];

  #read the third line of the file, containing measure names
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  #shift off the first (empty) field from the measure list
  shift(@columns);

  #build a tabular header line, and write it out
  $outputCols[0] = "Geog";
  $outputCols[1] = "Time";
  $outputCols[2] = "Prod";
  push(@outputCols, @columns);
  $csv->combine(@outputCols);
  $line = $csv->string();
  print OUTPUT "$line\n";

  #read the fourth line from the file, containing time period
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();
  $time = $columns[0];

  #cycle through the data in the source file, converting to tabular
  while ($line = <INPUT>)
  {
    #parse the CSV line we just took in, and convert to array
    $csv->parse($line);
    @columns = $csv->fields();

    #if the line begins a dimension info block (contains "Analyzer Report")
    if ($line =~ m/Analyzer Report/)
    {

      #read the next line, contains geography as first field
      $line = <INPUT>;
      $csv->parse($line);
      @columns = $csv->fields();
      $geog = $columns[0];

      #burn the next line, contains redundant measure info
      $line = <INPUT>;

      #read the next line, contains time as first field
      $line = <INPUT>;
      $csv->parse($line);
      @columns = $csv->fields();
      $time = $columns[0];

      #and we'll read/parse the next line of data, and continue processing
      $line = <INPUT>;
      $csv->parse($line);
      @columns = $csv->fields();
    }

    #if the line starts a new time period (only first column contains data)
    if ((length($columns[1]) < 1) || ($columns[1] =~ m/^\s+$/))
    {
      #set the time variable
      $time = $columns[0];

      #and we'll read/parse the next line of data, and continue processing
      $line = <INPUT>;
      $csv->parse($line);
      @columns = $csv->fields();
    }

    #convert line of data to tabular format
    undef(@outputCols);
    $outputCols[0] = $geog;
    $outputCols[1] = $time;
    push(@outputCols, @columns);

    #convert array to CSV, and write to output file
    $csv->combine(@outputCols);
    $line = $csv->string();
    print OUTPUT "$line\n";
  }

  #close and flush out the input/output files
  close(INPUT);
  close(OUTPUT);

  #unlink the source file
  unlink("/opt/apache/app/tmp/$filename");

  #rename the output file to the source file's name
  rename("/opt/apache/app/tmp/$filename.1", "/opt/apache/app/tmp/$filename");
}



#-------------------------------------------------------------------------
#
# Convert a nested table-formatted file that follows the Nielsen layout into
# a tabular file.
#

sub prep_nested_nielsen_to_csv
{
  my ($csv, $line, $time, $geog, $prod);
  my (@columns, @outputCols);
  my ($INPUT, $OUTPUT);

  my ($prepDB, $filename) = @_;


  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  #open the input CSV file for reading
  open(INPUT, "/opt/apache/app/tmp/$filename") or die("prep_nested_nielsen_to_csv: Couldn't open $filename $!");

  #open our output CSV file
  open(OUTPUT, ">/opt/apache/app/tmp/$filename.1");

  #read the first line of the file, containing measure names
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  #shift off the first three (empty) fields from the source file
  shift(@columns);
  shift(@columns);
  shift(@columns);

  #build a tabular header line, and write it out
  $outputCols[0] = "Geog";
  $outputCols[1] = "Time";
  $outputCols[2] = "Prod";
  push(@outputCols, @columns);
  $csv->combine(@outputCols);
  $line = $csv->string();
  print OUTPUT "$line\n";

  #cycle through the data in the source file, converting to tabular
  while ($line = <INPUT>)
  {
    #parse the CSV line we just took in, and convert to array
    $csv->parse($line);
    @columns = $csv->fields();

    #if the line contains geography dimension info (only 1 item in 1st column)
    if ((length($columns[0]) > 1) && (length($columns[1]) < 1))
    {
      $geog = $columns[0];

      #read the next line (contains dimension info, or measure data)
      $line = <INPUT>;
      $csv->parse($line);
      @columns = $csv->fields();
    }

    #if the line contains time dimension info (only 1 item in 2nd column)
    if ((length($columns[0]) < 1) &&
        (length($columns[1]) > 1) &&
        (length($columns[2]) < 1))
    {
      $time = $columns[1];

      #read the next line (contains measure data)
      $line = <INPUT>;
      $csv->parse($line);
      @columns = $csv->fields();
    }

    #convert line of data to tabular format
    undef(@outputCols);
    $outputCols[0] = $geog;
    $outputCols[1] = $time;
    shift(@columns);
    shift(@columns);
    push(@outputCols, @columns);

    #convert array to CSV, and write to output file
    $csv->combine(@outputCols);
    $line = $csv->string();
    print OUTPUT "$line\n";
  }

  #close and flush out the input/output files
  close(INPUT);
  close(OUTPUT);

  #unlink the source file
  unlink("/opt/apache/app/tmp/$filename");

  #rename the output file to the source file's name
  rename("/opt/apache/app/tmp/$filename.1", "/opt/apache/app/tmp/$filename");
}



#-------------------------------------------------------------------------
#
# Convert a nested Nielsen Answers on Demand file into a tabular file.
#

sub prep_nested_aod_to_csv
{
  my ($csv, $line, $time, $geog, $prod, $upc, $geography, $product);
  my (@columns, @outputCols, @tmp);
  my ($INPUT, $OUTPUT);

  my ($prepDB, $filename) = @_;


  #instantiate our CSV object
  $csv = Text::CSV->new( {binary => 1} );

  #open the input CSV file for reading
  open(INPUT, "/opt/apache/app/tmp/$filename") or die("prep_nested_aod_to_csv: Couldn't open $filename $!");

  #open our output CSV file
  open(OUTPUT, ">/opt/apache/app/tmp/$filename.1");

  #burn first line of file if it contains the "Edit Data Selection" tag
  $line = <INPUT>;
  if ($line =~ m/^Edit Data/)
  {
    $line = <INPUT>;
  }

  #parse the first header line (contains geo and time info)
  #Market : ShopRite Total TA • Period : Latest 52 Wks - W/E 08/12/17 • Product
  $line =~ m/^.*? : (.*?) • /;
  $geography = $1;

  $line =~ m/ Period : (.*?) • /;
  $time = $1;

  if (length($geography) < 2)
  {
    $geography = "UNKNOWN";
  }
  if (length($time) < 2)
  {
    $time = "UNKNOWN";
  }

  #parse the primary header line (2nd line in extracted CSV)
  $line = <INPUT>;
  $csv->parse($line);
  @columns = $csv->fields();

  $columns[0] = "Product";

  #push our static columns out to the front of the headers array
  @tmp = ('Geo', 'Time', 'UPC');
  push(@tmp, @columns);

  #output the headers
  $csv->combine(@tmp);
  $line = $csv->string();
  print OUTPUT "$line\n";

  while ($line = <INPUT>)
  {
    $csv->parse($line);
    @columns = $csv->fields();

    #extract the UPC from the end of the product name
    $product = $columns[0];
    $upc = "";
    if ($product =~ m/^.* (\d+)$/)
    {
      $upc = $1;
    }

    #skip rows without UPCs
    if (length($upc) < 9)
    {
      next;
    }

    @tmp = ($geography, $time, $upc);
    push(@tmp, @columns);

    $csv->combine(@tmp);
    $line = $csv->string();

    print OUTPUT "$line\n";
  }

  #close and flush out the input/output files
  close(INPUT);
  close(OUTPUT);

  #unlink the source file
  unlink("/opt/apache/app/tmp/$filename");

  #rename the output file to the source file's name
  rename("/opt/apache/app/tmp/$filename.1", "/opt/apache/app/tmp/$filename");
}



#-------------------------------------------------------------------------
#
# Run through every uploaded file associated with the current job,
# detect any that are in nested format, and convert them to tabular. Returns
# a hash of every file name and the detected layout for display purposes.
#

sub prep_flow_nested_to_tabular
{
  my ($query, $dbOutput, $fileID, $userFilename, $filename, $tabname, $tabID);
  my ($key, $line, $runningJobs, $opInfo, $q_opInfo, $numFiles, $pct);
  my ($filesProcessed, $status, $state);
  my (%convertedFiles);

  my ($prepDB, $kapDB, $flowID, $jobID, $userID) = @_;


  flow_telemetry($prepDB, $jobID, "Starting conversion of recognized nested layouts");
  PrepUtils_store_scaled_cloud_load($prepDB, $jobID);

  #check the job's state to make sure it's OK for us to run (should be after
  #the EXTRACT-DATA operation)
  $query = "SELECT opInfo, state FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($opInfo, $state) = $dbOutput->fetchrow_array;
  if ($state ne "EXTRACT-DATA")
  {
    flow_telemetry($prepDB, $jobID, "WARNING: nested_to_tabular called out of sequence: $state");
    return;
  }

  #set the "title" of the operation for UI
  PrepUtils_set_job_op_title($prepDB, $jobID, "Detecting and converting nested table layouts");

  #if the system is fully loaded, back off and wait for an open process slot
  $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);

  # Set a maximum wait time (30 minutes = 15 attempts at 120 seconds each)
  my $maxWaitAttempts = 15;
  my $waitAttempts = 0;

  while (!$okToRun && $waitAttempts < $maxWaitAttempts)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Waiting in processing queue', state='NESTED-WAIT'
        WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_details($prepDB, $jobID, "Waiting in processing queue (attempt $waitAttempts of $maxWaitAttempts)");

    sleep(120);
    $waitAttempts++;

    $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
  }

  # If we've reached the maximum wait time and still can't run, log it and continue anyway
  if (!$okToRun)
  {
    flow_telemetry($prepDB, $jobID, "WARNING: Maximum wait time exceeded, proceeding with nested conversion despite system load");
  }

  #set initial status
  $startTime = PrepUtils_get_current_timestamp($prepDB);
  $opInfo = "0|Detecting nested tables";
  $q_opInfo = $prepDB->quote($opInfo);
  $query = "UPDATE prep.jobs SET PID=$$, opInfo=$q_opInfo, state='NESTED-CONVERT'
      WHERE ID=$jobID";
  $prepDB->do($query);

  #grab every file we extracted data from as part of this job run
  $query = "SELECT ID, userFilename, filename, tabname, tabID FROM prep.files
      WHERE jobID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $numFiles = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $numFiles, $query);

  undef(%convertedFiles);
  $filesProcessed = 0;
  while (($fileID, $userFilename, $filename, $tabname, $tabID) = $dbOutput->fetchrow_array)
  {
    PrepUtils_set_job_op_details($prepDB, $jobID, "Normalizing nested data in <TT>$userFilename</TT>");
    $filesProcessed++;

    #update detailed status info
    $pct = ($filesProcessed / $numFiles) * 100;
    $pct = int($pct);
    PrepUtils_set_job_op_pct($prepDB, $jobID, "$pct");

    #open input file, and read first line
    open(INPUT, "/opt/apache/app/tmp/$filename");
    $line = <INPUT>;

    #handle optional refresh tag from Nielsen Answers nested data
    if ($line =~ m/Edit Data Selections/)
    {
      $line = <INPUT>;
    }

    close(INPUT);

    $key = "$userFilename|$tabname";

    #see if we're dealing with a Nielsen nested file (A1, B1, C1 are blank,
    #D1 has text)
    if ($line =~ m/^\,\,\,.*/)
    {
      flow_telemetry($prepDB, $jobID, "Normalizing nested Nielsen data in $userFilename $tabname");
      prep_nested_nielsen_to_csv($prepDB, $filename);
      $convertedFiles{$key} = "Nested Nielsen";
    }

    #see if we're dealing with an IRIv2 file ("Analyzer Report" appears
    #somewhere in the first line of data)
    elsif ($line =~ m/Analyzer Report/)
    {
      flow_telemetry($prepDB, $jobID, "Normalizing nested IRI analyzer data in $userFilename $tabname");
      prep_nested_iriv2_to_csv($prepDB, $filename);
      $convertedFiles{$key} = "Nested IRI (Analyzer)";
    }

    #see if we're dealing with an IRIv1 file (A1 blank, B1 has text)
    elsif ($line =~ m/^\,.*/)
    {
      flow_telemetry($prepDB, $jobID, "Normalizing nested IRI data in $userFilename $tabname");
      prep_nested_iriv1_to_csv($prepDB, $filename);
      $convertedFiles{$key} = "Nested IRI";
    }

    #see if we're dealing with a nested AOD file (1st or 2nd line with mkt data)
    elsif ($line =~ m/^.*? : .* Period : .* Product Share Basis/)
    {
      flow_telemetry($prepDB, $jobID, "Normalizing nested Nielsen Answers data in $userFilename $tabname");
      prep_nested_aod_to_csv($prepDB, $filename);
      $convertedFiles{$key} = "Nested Nielsen Answers";
    }

    else
    {
      $convertedFiles{$key} = "Tabular";
    }
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  PrepUtils_set_job_op_speed($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  flow_telemetry($prepDB, $jobID, "Finished converting nested layouts");

  $query = "UPDATE prep.jobs SET PID=NULL, opInfo='DONE', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);

  return(%convertedFiles);
}



#-------------------------------------------------------------------------
#
# Run through every uploaded file associated with the current job,
# and load the data into separate raw data tables. Apply any parsing options
# associated with the job as we do the loading.
#

sub prep_flow_load_raw_data
{
  my ($csv, $query, $dbOutput, $colSubq, $dataTable, $idx, $colref, $i);
  my ($INPUT, $rowsProcessed, $dataRows, $pct, $opInfo, $q_opInfo, $tmp);
  my ($separator, $sepChar, $skipTop, $skipTopRows, $headers, $headerRows);
  my ($compressWS, $trimWS, $blanks, $parseOptions, $filename, $records);
  my ($totalFiles, $fileID, $tabID, $colName, $userFilename, $tabname);
  my ($rowCount, $filesProcessed, $colTable, $q_name, $valSubq, $val, $q_val);
  my ($runningJobs, $rowsP, $dataR, $emptyNACols, $valSet, $status, $state);
  my ($geoColIdx, $mktTrim, $numColumns, $dataProvider, $colSQLType);
  my (@cols, @columns, @hdrArray, @colCount, @values, @valuesArray);
  my (%clientMktTrimHash);

  my ($prepDB, $kapDB, $flowID, $jobID, $userID) = @_;


  flow_telemetry($prepDB, $jobID, "Starting load of raw data");
  PrepUtils_store_scaled_cloud_load($prepDB, $jobID);

  #check the job's state to make sure it's OK for us to run (we should be in
  #the PARSE-WAIT state)
  $query = "SELECT state FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($state) = $dbOutput->fetchrow_array;
  if ($state ne "PARSE-WAIT")
  {
    flow_telemetry($prepDB, $jobID, "WARNING: load_raw_data called out of sequence: $state");
    return;
  }

  PrepUtils_set_job_op_title($prepDB, $jobID, "Loading raw data");

  #set default parsing options
  $separator = "comma";
  $sepChar = ",";
  $skipTop = "";
  $skipTopRows = 0;
  $headers = "1";
  $headerRows = 1;
  $compressWS = "1";
  $trimWS = "1";
  $blanks = "zeroes";

  #if the system is fully loaded, back off and wait for an open process slot
  $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);

  # Set a maximum wait time (30 minutes = 15 attempts at 120 seconds each)
  my $maxWaitAttempts = 15;
  my $waitAttempts = 0;

  while (!$okToRun && $waitAttempts < $maxWaitAttempts)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0| |Waiting in processing queue', state='LOAD-DATA-WAIT'
        WHERE ID=$jobID";
    $prepDB->do($query);
    PrepUtils_set_job_op_details($prepDB, $jobID, "Waiting in processing queue (attempt $waitAttempts of $maxWaitAttempts)");

    sleep(120);
    $waitAttempts++;

    $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
  }

  # If we've reached the maximum wait time and still can't run, log it and continue anyway
  if (!$okToRun)
  {
    flow_telemetry($prepDB, $jobID, "WARNING: Maximum wait time exceeded, proceeding with data loading despite system load");
  }

  #set initial status
  $startTime = PrepUtils_get_current_timestamp($prepDB);
  $opInfo = "0| |Loading raw data";
  $q_opInfo = $prepDB->quote($opInfo);
  $query = "UPDATE prep.jobs SET PID=$$, opInfo=$q_opInfo, state='LOAD-DATA'
      WHERE ID=$jobID";
  $prepDB->do($query);

  #pull pre-defined parsing options from database
  $query = "SELECT parseOptions, sourceInfo FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($parseOptions, $sourceInfo) = $dbOutput->fetchrow_array;

  if (($parseOptions =~ m/^SEPARATOR=(.*?)\|SEPCHAR=(.*?)\|SKIPTOP=(.*?)\|SKIPTOPROWS=(.*?)\|HEADERS=(.*?)\|HEADERROWS=(.*?)\|COMPRESSWS=(.*?)\|TRIMWS=(.*?)\|BLANKSZEROES=(.*?)\|MKTTRIM=(.*)$/) ||
      ($parseOptions =~ m/^SEPARATOR=(.*?)\|SEPCHAR=(.*?)\|SKIPTOP=(.*?)\|SKIPTOPROWS=(.*?)\|HEADERS=(.*?)\|HEADERROWS=(.*?)\|COMPRESSWS=(.*?)\|TRIMWS=(.*?)\|BLANKSZEROES=(.*)$/))
  {
    $separator = $1;
    $sepChar = $2;
    $skipTop = $3;
    $skipTopRows = $4;
    $headers = $5;
    $headerRows = $6;
    $compressWS = $7;
    $trimWS = $8;
    $blanks = $9;
    $mktTrim = $10;
  }

  #else invalid parsing options - stop with an error message
  else
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, mode='interactive', opInfo='ERR|Invalid parsing options', state='ERROR', runProgress='ERROR', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
    exit;
  }

  flow_telemetry($prepDB, $jobID, "Using parse options SEPARATOR=$separator|SEPCHAR=$sepChar|SKIPTOP=$skipTop|SKIPTOPROWS=$skipTopRows|HEADERS=$headers|HEADERROWS=$headerRows|COMPRESSWS=$compressWS|TRIMWS=$trimWS|BLANKSZEROES=$blanks|MKTTRIM=$mktTrim");

  #for efficiency reasons inside if statements, turn parsing settings into numbers
  ($compressWS eq "on") ? $compressWS = 1 : $compressWS = 0;
  ($trimWS eq "on") ? $trimWS = 1 : $trimWS = 0;
  ($blanks eq "zeroes") ? $blanksZeroes = 1 : $blanksZeroes = 0;

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = OFF";
  $prepDB->do($query);

  #get the number of rows of data to be loaded
  #NB: This extra DB query and file access is worth it in customer calmness
  $dataRows = 0;
  $query = "SELECT filename, userFilename FROM prep.files WHERE jobID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($filename, $userFilename) = $dbOutput->fetchrow_array)
  {
    PrepUtils_set_job_op_details($prepDB, $jobID, "Examining records in <TT>$userFilename</TT>");
    $records = `wc -l \"/opt/apache/app/tmp/$filename\"`;
    $records =~ m/^(\d+) /;
    $records = $1;
    $dataRows = $dataRows + $records;
    flow_telemetry($prepDB, $jobID, "Found $records rows to process in $filename");
  }

  $query = "UPDATE prep.job_history
      SET recordsProcessed = $dataRows WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  $dataR = prep_autoscale_number($dataRows);
  PrepUtils_set_job_op_title($prepDB, $jobID, "Loading $dataR records from raw data");
  $query = "UPDATE prep.jobs SET rowCount=$dataRows WHERE ID=$jobID";
  $prepDB->do($query);

  #grab every file we extracted data from as part of this job run
  $query = "SELECT ID, userFilename, filename, tabname, tabID FROM prep.files
      WHERE jobID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $totalFiles = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $totalFiles, $query);

  $filesProcessed = 0;
  $rowsProcessed = 0;
  while (($fileID, $userFilename, $filename, $tabname, $tabID) = $dbOutput->fetchrow_array)
  {

    flow_telemetry($prepDB, $jobID, "Starting load of $userFilename $tabname");
    PrepUtils_set_job_op_details($prepDB, $jobID, "<b>Loading from:</b> <TT>$userFilename</TT>");

    #open input file
    open($INPUT, "</opt/apache/app/tmp/$filename");
    $csv = Text::CSV_XS->new( {binary => 1, sep_char => $sepChar} );

    #if the user told us to burn a couple lines at the top of the file
    if (($skipTop eq "on") && ($skipTopRows > 0))
    {

      #burn the specified number of lines
      for($i=0; $i < $skipTopRows; $i++)
      {
        $colref = $csv->getline($INPUT);
      }
    }

    #read first line of file (should contain our headers)
    $colref = $csv->getline($INPUT);
    @columns = @$colref;

    #if we were told the file contains headers, use them
    if ($headers eq "on")
    {

      #handle Nielsen AOD columns (names are bracketed)
      $idx = 0;
      foreach $tmp (@columns)
      {
        if ($tmp =~ m/^\[(.*)\]$/)
        {
          $columns[$idx] = $1;
        }
        $idx++;
      }

      #if we're dealing with headers split across multiple rows
      if ($headerRows > 1)
      {

        #append following rows to the name of the column in Koala
        $rowCount = 1;
        while ($rowCount < $headerRows)
        {
          $colref = $csv->getline($INPUT);
          @hdrArray = @$colref;

          $idx = 0;
          foreach $tmp (@hdrArray)
          {
            $columns[$idx] .= " - " . $tmp;
            $idx++;
          }

          $rowCount++;
        }
      }
    }

    #else use generic column names
    else
    {

      #use the first line to get a count for how many columns we have
      @colCount = $csv->fields;

      undef(@columns);
      $idx = 1;
      foreach $tmp (@colCount)
      {
        $colName = "column_" . $idx;
        push(@columns, $colName);
        $idx++;
      }
    }

    $dataProvider = PrepUtils_determine_data_provider(@columns);

    #determine if we're going to use a custom client call-out for loading
    $customBairBrandRewrite = 0;
    if ($Lib::KoalaConfig::cloudname eq "bair")
    {
      if ($sourceInfo =~ m/bair\-idw/i)
      {
        $customBairBrandRewrite = 1;
      }
    }

    #build table name strings
    $colTable = "prep_data.$jobID" . "_" . $fileID . "_" . $tabID . "_cols";
    $dataTable = "prep_data.$jobID" . "_" . $fileID . "_" . $tabID;

    #drop an old columns table if it exists
    $query = "DROP TABLE IF EXISTS $colTable";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    #create the columns table for this file
    $query = "CREATE TABLE $colTable
        (
          ID INT UNSIGNED NOT NULL,
          name VARCHAR(128) NOT NULL,
          dataType VARCHAR(32)
        )";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    #load the columns data into the columns table
    $idx = 0;
    $colSubq = "";
    $geoColIdx = -1;
    foreach $colName (@columns)
    {
      if (length($colName) < 1)
      {
        $colName = "Column $idx";
      }

      if ($colName eq "Market Display Name")
      {
        $geoColIdx = $idx;
      }
      elsif ($colName eq "Market Description")
      {
        $geoColIdx = $idx;
      }
      elsif ($colName eq "Market Sum")
      {
        $geoColIdx = $idx;
      }
      elsif ($colName eq "BRAND")
      {
        $brandSegColIdx = $idx;
      }
      elsif ($colName eq "BRAND FAMILY")
      {
        $brandFamilySegColIdx = $idx;
      }
      elsif ($colName eq "BRAND LOW")
      {
        $brandLowSegColIdx = $idx;
      }

      #build column name subquery (used for bulk insert later)
      $colSubq .= "column_$idx,";

      $q_name = $prepDB->quote($colName);
      $query = "INSERT INTO $colTable (ID, name) VALUES ($idx, $q_name)";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);

      $idx++;
    }
    chop($colSubq);

    #drop an old data table if it exists
    $query = "DROP TABLE IF EXISTS $dataTable";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    #create the data table
    $query = "CREATE TABLE $dataTable
        (
        ID INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
        valid TINYINT UNSIGNED DEFAULT 0,
          ";

    $idx = 0;
    foreach $colName (@columns)
    {
      $colSQLType = PrepUtils_determine_column_sql_type($dataProvider, $colName);
      #$query .= "column_$idx $colSQLType, ";
      $query .= "column_$idx VARCHAR(127), ";
      $idx++;
    }

    chop($query);     chop($query);
    $query .= ")";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    #if we're doing a market pre-trim, get the specified trim hash
    undef(%clientMktTrimHash);
    if ($mktTrim ne "0")
    {
      %clientMktTrimHash = prep_client_trim_hash($mktTrim);
    }

    #get our (more or less) start time for time estimation
    $lastTime = time();
    $lastTimeCount = 0;

    #load the data from the file into the data table
    #NB: We build up several SQL VALUES sets at a time in valuesArray, and then
    #    do a bulk insert for performance reasons.
    undef(@valuesArray);
    $errorCount = 0;
    $numColumns = scalar(@columns);
    while (!eof($INPUT))
    {
      $colref = $csv->getline($INPUT);
      if (!defined($colref))
      {
        if ($errorCount <= 10)
        {
          $badline = $csv->error_input;
          ($csvErrCode, $csvErrStr, $csvErrPos, $csvErrLine) = $csv->error_diag ();
          flow_telemetry($prepDB, $jobID, "ERROR: Unable to parse line $csvErrLine of $userFilename\n$csvErrStr at character $csvErrPos\n$badline");

          $html = "<P>Unable to parse line <CODE>$csvErrLine</CODE> of <CODE>$userFilename</CODE> <BR> $csvErrStr at character <CODE>$csvErrPos</CODE></P>";
          $q_html = $prepDB->quote($html);
          $query = "UPDATE prep.job_history
              SET linesDiscardedDetails = CONCAT(linesDiscardedDetails, $q_html)
              WHERE jobID=$jobID";
          $status = $prepDB->do($query);
          PrepUtils_handle_db_err($prepDB, $status, $query);
        }
        elsif ($errorCount == 11)
        {
          flow_telemetry($prepDB, $jobID, "     ...and many more.");
        }
        $errorCount++;
      }

      #update status info for UI
      if ($rowsProcessed % 2000 == 0)
      {
        $remRows = $dataRows - $rowsProcessed;
        $remRows = prep_autoscale_number($remRows);
        PrepUtils_set_job_op_extra($prepDB, $jobID, "<b>Records remaining:</b> $remRows");

        $pct = ($rowsProcessed / $dataRows) * 100;
        $pct = int($pct);
        PrepUtils_set_job_op_pct($prepDB, $jobID, "$pct");

        #if it's been at least 5 seconds since we started, update speed stats
        $interval = time() - $lastTime;
        if ($interval >= 5)
        {
          $intervalDone = $rowsProcessed - $lastTimeCount;
          $opSpeed = int($intervalDone / $interval);
          $lastTime = time();
          $lastTimeCount = $rowsProcessed;
          PrepUtils_set_job_op_speed($prepDB, $jobID, $opSpeed);
        }
      }

      $rowsProcessed++;

      #update detailed status info
      if (($rowsProcessed % 2000) == 0)
      {
        $pct = ($rowsProcessed / $dataRows) * 100;
        $pct = int($pct);

        $rowsP = prep_autoscale_number($rowsProcessed);
        $opInfo = "$pct|$rowsP of $dataR records|Loading $userFilename";
        $q_opInfo = $prepDB->quote($opInfo);
        $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
        $prepDB->do($query);
      }

      #handle autotrimming of unused markets
      if ((%clientMktTrimHash) && ($geoColIdx >= 0))
      {
        if ($clientMktTrimHash{@$colref[$geoColIdx]} != 1)
        {
          next;
        }
      }

      #implement custom BAIR rewrite code for BRAND columns in Connect data
      #TODO: refactor this as a separate routine as part of a standard
      #   call-out area in case we need to generalize for other clients
      if (($customBairBrandRewrite > 0) && ($brandSegColIdx > 0))
      {
        if (@$colref[$brandSegColIdx] =~ m/^NO BRAND LISTED \((.*)\)/)
        {
          @$colref[$brandSegColIdx] = $1;
        }
        elsif (@$colref[$brandSegColIdx] =~ m/(.*) \(.*\)/)
        {
          @$colref[$brandSegColIdx] = $1;
        }
      }
      if (($customBairBrandRewrite > 0) && ($brandFamilySegColIdx > 0))
      {
        if (@$colref[$brandFamilySegColIdx] =~ m/^NO BRAND LISTED \((.*)\)/)
        {
          @$colref[$brandFamilySegColIdx] = $1;
        }
        elsif (@$colref[$brandFamilySegColIdx] =~ m/(.*) \(.*\)/)
        {
          @$colref[$brandFamilySegColIdx] = $1;
        }
      }
      if (($customBairBrandRewrite > 0) && ($brandLowSegColIdx > 0))
      {
        if (@$colref[$brandLowSegColIdx] =~ m/^NO BRAND LISTED \((.*)\)/)
        {
          @$colref[$brandLowSegColIdx] = $1;
        }
        elsif (@$colref[$brandLowSegColIdx] =~ m/(.*) \(.*\)/)
        {
          @$colref[$brandLowSegColIdx] = $1;
        }
      }

      #build up the values subquery
      $valSubq = "";
      foreach $val (@$colref)
      {

        #if we're compressing white space
        if ($compressWS)
        {
          $val =~ s/\s+/ /g;
        }

        #if we're trimming white space off the front and end of values
        if ($trimWS)
        {
          $val =~ s/^\s+//;
          $val=~ s/\s+$//;
        }

        #turn an NA into a NULL
        if (lc($val) eq "na")
        {
          undef($val);
        }

        #if we have an explicit % symbol
        elsif ($val =~ m/^([\d,\.]+)\%$/)
        {
          $val = $1;
        }

        #if we're a number in scientific notation
=pod
        elsif ($val =~ m/^\-?[\d,\.]+E[\-,\+]\d+$/)
        {
          $val = sprintf("%.10f", $val);
        }
=cut

        $valLength = length($val);

        #if we're turning blanks into zeroes
        if (($blanksZeroes) && ($valLength < 1))
        {
          $val = 0;
        }

        #else blanks are NULLs
        elsif ($valLength < 1)
        {
          undef($val);
        }

        #if the value is over 128 characters, chop out middle to make it fit
        elsif ($valLength > 127)
        {
          $front = substr($val, 0, 60);
          $back = substr($val, -60);
          $val = $front . "..." . $back;
          $tmp = length($val);
        }

        $q_val = $prepDB->quote($val);

        $valSubq .= "$q_val,";
      }

      #NB: some particularly nasty Nielsen data doesn't include blanks for NA
      #    at the end of records, so we have to handle it ourselves
      $emptyNACols = $numColumns - scalar(@$colref);
      for ($i = 0; $i < $emptyNACols; $i++)
      {
        $valSubq .= "NULL,";
      }
      chop($valSubq);

      #add this record's values to the array for bulk insertion
      push(@valuesArray, "($valSubq), ");

      #if we have 100 records ready for bulk insertion, build & run the SQL
      if (scalar(@valuesArray) > 99)
      {
        $query = "INSERT INTO $dataTable ($colSubq) VALUES ";
        foreach $valSet (@valuesArray)
        {
          $query .= "$valSet";
        }
        chop($query);  chop($query);

        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);

        undef(@valuesArray);
      }
    }

    #bulk insert any remaining records in the valueArray to finish out the file
    if (scalar(@valuesArray) > 0)
    {
      $query = "INSERT INTO $dataTable ($colSubq) VALUES ";
      foreach $valSet (@valuesArray)
      {
        $query .= "$valSet";
      }
      chop($query);  chop($query);

      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);

      undef(@valuesArray);
    }

    # --- done loading the current file ---

    $filesProcessed++;

    close($INPUT);
    unlink("/opt/apache/app/tmp/$filename");
  }

  #store the number of unparseable lines in the detailed job history table
  $query = "UPDATE prep.job_history SET linesDiscarded=$errorCount
      WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  PrepUtils_set_job_op_speed($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  flow_telemetry($prepDB, $jobID, "Finished loading raw data");

  $query = "UPDATE prep.jobs SET PID=NULL, opInfo='DONE', lastAction=NOW() \
      WHERE ID=$jobID";
  $prepDB->do($query);
}



#-------------------------------------------------------------------------
#
# Check the previous settings for file type (data, lookup, etc) and set the
# types for our current run. Anything we can't find becomes a data file by
# default.
#

sub prep_flow_set_file_types
{
  my ($query, $dbOutput, $name, $tabName, $type, $key, $id, $userFilename);
  my ($status);
  my (%fileTypes);

  my ($prepDB, $flowID, $jobID) = @_;


  undef(%fileTypes);
  $query = "SELECT name, tabName, type FROM prep.file_types WHERE flowID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  while (($name, $tabName, $type) = $dbOutput->fetchrow_array)
  {

    #deal with the kludge we had to use to get the DB primary key to work
    if ($tabName eq " ")
    {
      $tabName = "";
    }
    $key = "$name-$tabName";
    $fileTypes{$key} = $type;
  }

  #get every file/tab uploaded by the user in this run
  $query = "SELECT ID, userFilename, tabName FROM prep.files WHERE jobID=$jobID
      ORDER by userFilename, tabName";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  while (($id, $userFilename, $tabName) = $dbOutput->fetchrow_array)
  {
    $key = "$userFilename-$tabName";
    $type = $fileTypes{$key};

    #if we don't have a previously specified file type, it's probably data
    if (length($type) < 1)
    {

      #but we'd be fools not to pick up on really obvious signals
      if ($key =~ m/lookup/i)
      {
        $type = "lookup";
      }
      else
      {
        $type = "data";
      }
    }

    $query = "UPDATE prep.files SET type='$type' WHERE ID=$id";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }
}



#-------------------------------------------------------------------------
#
# Do the shortcut version of the data merge operation when there's only 1
# data file (rename the tables and make some minor alterations).
#

sub shortcut_prep_flow_merge_data
{
  my ($query, $masterTable, $masterColTable, $dbOutput, $fileID, $tabID);
  my ($colTable, $colID, $colName, $dataTable, $i, $j, $oldColName, $rowCount);
  my ($newColName, $status);

  my ($prepDB, $flowID, $jobID) = @_;


  flow_telemetry($prepDB, $jobID, "Performing data merge shortcut");

  #NB: we're called from the regular merge function, which handles state
  #    checking and load balancing - that's why we don't do any here

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "Performing optimized data merge");

  #set initial status
  $query = "UPDATE prep.jobs SET PID=$$, opInfo='0| |Analyzing data to be merged', state='MERGE-DATA'
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Analyzing data to be merged");

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #drop old data and column tables if they exist
  $query = "DROP TABLE IF EXISTS $masterTable";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
  $query = "DROP TABLE IF EXISTS $masterColTable";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #get the data file we're working with
  $query = "SELECT ID, tabID FROM prep.files WHERE jobID=$jobID AND type='data'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  ($fileID, $tabID) = $dbOutput->fetchrow_array;
  $dataTable = "prep_data.$jobID" . "_" . $fileID . "_" . $tabID;
  $colTable = "prep_data.$jobID" . "_" . $fileID . "_" . $tabID . "_cols";

  $query = "UPDATE prep.jobs SET opInfo='10| |Repurposing extant data', state='MERGE-DATA'
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Reflowing existing data");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "10");

  #rename data table to new master table name
  $query = "RENAME TABLE $dataTable TO $masterTable";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #remove the old table name from the list of files associated with the job
  $query = "DELETE FROM prep.files WHERE ID=$fileID AND tabID=$tabID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  $query = "UPDATE prep.jobs SET opInfo='50| |Setting up columns', state='MERGE-DATA'
      WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Setting up columns");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "50");

  #create the master columns table
  $query = "CREATE TABLE $masterColTable
      (
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT,
        name VARCHAR(128) NOT NULL,
        type VARCHAR(24),
        PRIMARY KEY(ID)
      )";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #populate the master columns table
  $query = "SELECT ID, name FROM $colTable ORDER BY ID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($colID, $colName) = $dbOutput->fetchrow_array)
  {
    $query = "INSERT INTO $masterColTable (name) VALUES ('$colName')";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    $i = $colID;
  }

  #drop the file's column table we no longer need
  $query = "DROP TABLE $colTable";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #NB: $i still contains the highest-numbered column from the source table
  #rename the columns in the data table to match the master format
  while ($i >= 0)
  {
    $j = $i + 1;
    $oldColName = "column_" . $i;
    $newColName = "column_" . $j;

    $query = "ALTER TABLE $masterTable
        CHANGE COLUMN $oldColName $newColName VARCHAR(127) NULL DEFAULT NULL";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    $i--;
  }

  $query = "UPDATE prep.jobs
      SET opInfo='60| |Populating metadata', state='MERGE-DATA' WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Populating metadata");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "60");

  #calculate the number of data rows in the master table, and store it
  $query = "UPDATE prep.jobs
      SET opInfo='70| |Calculating number of data rows', state='MERGE-DATA'
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Calculating number of data rows");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "70");

  flow_telemetry($prepDB, $jobID, "Calculating number of data rows in job");
  $query = "SELECT COUNT(*) FROM $masterTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($rowCount) = $dbOutput->fetchrow_array;

  $query = "UPDATE prep.jobs SET rowCount=$rowCount WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #create a quick index on the lookup column (column_0) of any lookup tables
  $query = "UPDATE prep.jobs
      SET opInfo='80| |Indexing lookup tables', state='MERGE-DATA'
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Indexing lookup tables");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "80");

  flow_telemetry($prepDB, $jobID, "Indexing primary column in lookup tables");
  $query = "SELECT ID, tabID FROM prep.files WHERE jobID=$jobID AND type='lookup'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($fileID, $tabID) = $dbOutput->fetchrow_array)
  {
    $dataTable = "prep_data.$jobID" . "_" . $fileID . "_" . $tabID;

    $query = "CREATE INDEX idx_lookup ON $dataTable (column_0)";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  PrepUtils_set_job_op_speed($prepDB, $jobID, "");

  flow_telemetry($prepDB, $jobID, "Finished data merge");

  $query = "UPDATE prep.jobs SET PID=NULL, opInfo='DONE', lastAction=NOW()
      WHERE ID = $jobID";
  $prepDB->do($query);
}



#-------------------------------------------------------------------------
#
# Merge all of the individual file/tab database tables together into one
# giant table suitable for transform/normalization options.
#

sub prep_flow_merge_data
{
  my ($dbOutput, $dbOutput1, $dbOutputFile, $db_src, $query, $pct, $opInfo);
  my ($masterTable, $masterColTable, $dataRows, $fileID, $tabID, $dataTable);
  my ($records, $start, $end, $curPos, $rowsProcessed, $dataRows, $q_opInfo);
  my ($userFilename, $tabname, $colTable, $colID, $colName, $key, $fileColIdx);
  my ($masterColIdx, $q_colName, $colSubQ, $valSubQ, $col, $status, $val, $str);
  my ($srcQuery, $dataValStr, $rowCount, $runningJobs, $rowsP, $dataR, $valSet);
  my ($state);
  my (@dataVals, @valuesArray);
  my (%masterColHash, %fileColHash, %fileColMapHash);

  my ($prepDB, $kapDB, $flowID, $jobID, $userID) = @_;


  flow_telemetry($prepDB, $jobID, "Starting data merge");
  PrepUtils_store_scaled_cloud_load($prepDB, $jobID);

  #check the job's state to make sure it's OK for us to run (should be after
  #the EXTRACT-DATA operation)
  $query = "SELECT opInfo, state FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($opInfo, $state) = $dbOutput->fetchrow_array;
  if ($state ne "DATATYPE-WAIT")
  {
    flow_telemetry($prepDB, $jobID, "WARNING: merge_data called out of sequence: $state");
    return;
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "Merging data");

  #if the system is fully loaded, back off and wait for an open process slot
  $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
  while (!$okToRun)
  {
    $query = "UPDATE prep.jobs
        SET PID=$$, opInfo='0| |Waiting in processing queue', state='MERGE-DATA-WAIT'
        WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_details($prepDB, $jobID, "Waiting in processing queue");

    sleep(120);

    $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #NB: If we only loaded 1 data tab in this job, we can use a "shortcut" to
  #    avoid essentially duplicating the same data - we just rename the
  #    existing data and columns tables, and make some minor alterations
  $query = "SELECT ID, userFilename, tabname, tabID FROM prep.files
      WHERE jobID=$jobID AND type='data'";
  $dbOutputFile = $prepDB->prepare($query);
  $status = $dbOutputFile->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  if ($status == 1)
  {
    shortcut_prep_flow_merge_data($prepDB, $flowID, $jobID);
    PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
    return;
  }

  #set initial status
  $pct = 0;
  $opInfo = "0| |Analyzing data to be merged";
  $q_opInfo = $prepDB->quote($opInfo);
  $query = "UPDATE prep.jobs SET opInfo=$q_opInfo, state='MERGE-DATA'
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Analyzing data to be merged");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #drop old data and column tables if they exist
  $query = "DROP TABLE IF EXISTS $masterTable";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
  $query = "DROP TABLE IF EXISTS $masterColTable";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #create the master columns table
  $query = "CREATE TABLE $masterColTable
      (
        ID INT UNSIGNED NOT NULL AUTO_INCREMENT,
        name VARCHAR(128) NOT NULL,
        type VARCHAR(24),
        PRIMARY KEY(ID)
      )";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
  undef(%masterColHash);

  #create the master data table
  $query = "CREATE TABLE $masterTable
      (
        ID INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
        valid TINYINT UNSIGNED DEFAULT 0
      )";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #get the number of rows of data to be loaded
  #NB: This extra DB query work is worth it in customer calmness
  $dataRows = 0;
  $query = "SELECT ID, tabID FROM prep.files WHERE jobID=$jobID AND type='data'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($fileID, $tabID) = $dbOutput->fetchrow_array)
  {
    $dataTable = "prep_data.$jobID" . "_" . $fileID . "_" . $tabID;

    $query = "SELECT COUNT(column_0) FROM $dataTable";
    $dbOutput1 = $prepDB->prepare($query);
    $status = $dbOutput1->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($records) = $dbOutput1->fetchrow_array();

    $dataRows += $records;
  }

  #grab every data file we loaded for this job
  $query = "SELECT ID, userFilename, tabname, tabID FROM prep.files
      WHERE jobID=$jobID AND type='data'";
  $dbOutputFile = $prepDB->prepare($query);
  $status = $dbOutputFile->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  $rowsProcessed = 0;
  while (($fileID, $userFilename, $tabname, $tabID) = $dbOutputFile->fetchrow_array)
  {
    flow_telemetry($prepDB, $jobID, "Merging data from $userFilename $tabname");

    PrepUtils_set_job_op_details($prepDB, $jobID, "Merging data from $userFilename");

    #build data table name strings
    $colTable = "prep_data.$jobID" . "_" . $fileID . "_" . $tabID . "_cols";
    $dataTable = "prep_data.$jobID" . "_" . $fileID . "_" . $tabID;

    #build up a hash of the file's columns
    undef(%fileColHash);
    $query = "SELECT ID, name FROM $colTable";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    while (($colID, $colName) = $dbOutput->fetchrow_array)
    {
      $fileColHash{$colName} = $colID;
    }

    #run through each column from the data file, and add it to the master
    #column table if it isn't already there
    undef(%fileColMapHash);
    foreach $colName (sort {$fileColHash{$a} cmp $fileColHash{$b}} keys %fileColHash)
    {
      $key = lc($colName);

      #if a column with the same name already exists in the master table
      if (defined($masterColHash{$key}))
      {

        #add a mapping entry so we know which master column to put the
        #matching file column into
        $fileColIdx = $fileColHash{$colName};
        $masterColIdx = $masterColHash{$key};
        $fileColMapHash{$fileColIdx} = $masterColIdx;
      }

      #else the column doesn't already exist in the master table
      else
      {

        #add the column to the master column table
        $q_colName = $prepDB->quote($colName);
        $query = "INSERT INTO $masterColTable (name) VALUES($q_colName)";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);

        #get the ID of the new master column
        $masterColIdx = $prepDB->{q{mysql_insertid}};

        #add the new column to the master column and mapping hashes
        $fileColIdx = $fileColHash{$colName};
        $masterColHash{$key} = $masterColIdx;
        $fileColMapHash{$fileColIdx} = $masterColIdx;

        #add the column to the master table
        $query = "ALTER TABLE $masterTable
            ADD COLUMN column_$masterColIdx VARCHAR(127)";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
      }
    }

    #build up the subquery for the master table columns and value selection
    #from the data table
    $colSubQ = "";
    $valSubQ = "";
    foreach $col (keys %fileColMapHash)
    {
      $masterColIdx = $fileColMapHash{$col};

      $colSubQ .= "column_$masterColIdx,";
      $valSubQ .= "column_$col,";
    }
    chop($colSubQ);
    chop($valSubQ);

    $curPos = -1;
    $status = 1;
    while ($status > 0)
    {
      $start = $curPos + 1;

      #build up the query to pull all of the source data from the data table
      $srcQuery = "SELECT $valSubQ FROM $dataTable LIMIT $start,100000";

      #select all of the data from the file's table in the mapped column order
      undef($db_src);
      $db_src = $prepDB->prepare($srcQuery);
      $status = $db_src->execute;
      PrepUtils_handle_db_err($prepDB, $status, $srcQuery);

      #NB: We build up several SQL VALUES sets at a time in valuesArray, and
      #    then do a bulk insert for performance reasons.
      undef(@valuesArray);
      while (@dataVals = $db_src->fetchrow_array)
      {

        #update detailed status info
        if (($rowsProcessed % 1000) == 0)
        {

          $pct = ($rowsProcessed / $dataRows) * 100;
          $pct = int($pct);

          $rowsP = prep_autoscale_number($rowsProcessed);
          $dataR = prep_autoscale_number($dataRows);
          $opInfo = "$pct|$rowsP of $dataR records|Merging $userFilename";
          $q_opInfo = $prepDB->quote($opInfo);
          $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
          $prepDB->do($query);

          PrepUtils_set_job_op_extra($prepDB, $jobID, "$rowsP of $dataR records");
          PrepUtils_set_job_op_pct($prepDB, $jobID, "$pct");
        }

        #convert values to string, quoting as we go
        $dataValStr = "";
        foreach $val (@dataVals)
        {
          $str = $prepDB->quote($val);
          $dataValStr .= "$str,";
        }
        chop($dataValStr);

        #add this record's values to the array for bulk insertion
        push(@valuesArray, "($dataValStr), ");

        #if we have 50 records ready for bulk insertion, build & run the SQL
        if (scalar(@valuesArray) > 49)
        {
          $query = "INSERT INTO $masterTable ($colSubQ) VALUES ";
          foreach $valSet (@valuesArray)
          {
            $query .= "$valSet";
          }
          chop($query);  chop($query);

          $status = $prepDB->do($query);
          PrepUtils_handle_db_err($prepDB, $status, $query);
          undef(@valuesArray);
        }

        $rowsProcessed++;
      }

      #bulk insert any remaining records in the valueArray to finish the chunk
      if (scalar(@valuesArray) > 0)
      {
        $query = "INSERT INTO $masterTable ($colSubQ) VALUES ";
        foreach $valSet (@valuesArray)
        {
          $query .= "$valSet";
        }
        chop($query);  chop($query);

        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);

        undef(@valuesArray);
      }

      $curPos += 100_000;
    }

    #delete the no-longer-needed data table
    $query = "DROP TABLE $dataTable";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
    $query = "DROP TABLE $colTable";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
    $query = "DELETE FROM prep.files WHERE ID=$fileID";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  #calculate the number of data rows in the master table, and store it
  flow_telemetry($prepDB, $jobID, "Calculating number of data rows in job");
  $query = "SELECT COUNT(*) FROM $masterTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($rowCount) = $dbOutput->fetchrow_array;

  $query = "UPDATE prep.jobs SET rowCount=$rowCount WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #create a quick index on the lookup column (column_0) of any lookup tables
  flow_telemetry($prepDB, $jobID, "Indexing primary column in lookup tables");
  $query = "SELECT ID, tabID FROM prep.files WHERE jobID=$jobID AND type='lookup'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($fileID, $tabID) = $dbOutput->fetchrow_array)
  {
    $dataTable = "prep_data.$jobID" . "_" . $fileID . "_" . $tabID;

    $query = "CREATE INDEX idx_lookup ON $dataTable (column_0)";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  PrepUtils_set_job_op_speed($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  flow_telemetry($prepDB, $jobID, "Finished data merge");

  $query = "UPDATE prep.jobs SET PID=NULL, opInfo='DONE', lastAction=NOW() \
      WHERE ID=$jobID";
  $prepDB->do($query);
}



#-------------------------------------------------------------------------
#
# Based on a column's human-readable name, determine what type of CPG data
# it probably contains. If we can't figure it out, return an empty string.
#

sub prep_flow_detect_column_type_by_name
{
  my ($tmp);

  my ($name) = @_;


  my %measureHintHash = (
      "\$" => 1,
      "\$ ya" => 1,
      "\$ 2ya" => 1,
      "\$ chg ya" => 1,
      "%acv" => 1,
      "%acv ya" => 1,
      "%acv reach" => 1,
      "%acv reach ya" => 1,
      "any price decr \$" => 1,
      "any price decr \$ ya" => 1,
      "any promo \$" => 1,
      "any promo \$ ya" => 1,
      "any promo units" => 1,
      "any promo units ya" => 1,
      "avg unit price" => 1,
      "avg unit price ya" => 1,
      "base \$" => 1,
      "base \$ ya" => 1,
      "base \$ 2ya" => 1,
      "base units" => 1,
      "base units ya" => 1,
      "base units 2ya" => 1,
      "disp w/o feat \$" => 1,
      "disp w/o feat \$ ya" => 1,
      "disp w/o feat units" => 1,
      "disp w/o feat units ya" => 1,
      "eq" => 1,
      "eq ya" => 1,
      "eq 2ya" => 1,
      "est acv selling" => 1,
      "est acv selling ya" => 1,
      "feat & disp \$" => 1,
      "feat & disp \$ ya" => 1,
      "feat & disp units" => 1,
      "feat & disp units ya" => 1,
      "feat w/o disp \$" => 1,
      "feat w/o disp \$ ya" => 1,
      "feat w/o disp units" => 1,
      "feat w/o disp units ya" => 1,
      "incr \$" => 1,
      "incr \$ ya" => 1,
      "incr units" => 1,
      "incr units ya" => 1,
      "no promo \$" => 1,
      "no promo \$ ya" => 1,
      "no promo units" => 1,
      "no promo units ya" => 1,
      "number of stores" => 1,
      "number of stores ya" => 1,
      "number of stores selling" => 1,
      "number of stores selling ya" => 1,
      "price decr \$" => 1,
      "price decr \$ ya" => 1,
      "price decr units" => 1,
      "price decr units ya" => 1,
      "price decr only \$" => 1,
      "price decr only \$ ya" => 1,
      "price decr only units" => 1,
      "price decr only units ya" => 1,
      "qual \$" => 1,
      "qual \$ ya" => 1,
      "subsidized \$" => 1,
      "subsidized \$ ya" => 1,
      "subsidized units" => 1,
      "subsidized units ya" => 1,
      "units" => 1,
      "units ya" => 1,
      "units 2ya" => 1,
  );

  my %psegHintHash = (
      "BC CATEGORY" => 1,
      "BC DEPARTMENT" => 1,
      "BC SUPER CATEGORY" => 1,
      "BRAND" => 1,
      "CATEGORY" => 1,
      "DEPARTMENT" => 1,
      "IM MULTI CHAR" => 1,
      "MANUFACTURER" => 1,
      "SEGMENT" => 1,
      "SUB CATEGORY" => 1,
      "SUPER CATEGORY" => 1,
  );

  #detect UPC/SKU/EAN column
  if (($name =~ m/^upc$/i) || ($name =~ m/universal prod code/i) ||
      ($name =~ m/^upc code$/i) || ($name =~ m/prod code/i) ||
      ($name =~ m/universal/i) || ($name =~ m/^ean$/i) ||
      ($name =~ m/^upc\s/i) || ($name =~ m/\supc$/i))
  {
    return("upc");
  }

  #detect product column
  elsif (($name =~ m/^prod$/i) || ($name =~ m/^product$/i) ||
         ($name =~ m/^products$/i) ||
         ($name =~ m/^short product description$/i) ||
         ($name =~ m/^long product description$/i) ||
         ($name =~ m/^item$/i))
  {
    return("product");
  }

  #detect geography column
  elsif (($name =~ m/^geo$/i) || ($name =~ m/^geog$/i) ||
         ($name =~ m/^geography$/i) || ($name =~ m/^geographies$/i) ||
         ($name =~ m/^mkt$/i) || ($name =~ m/^markets$/i) ||
         ($name =~ m/^market display name$/i) || ($name =~ m/^market description/i))
  {
    return("geography");
  }

  #detect time period column
  elsif (($name =~ m/^time$/i) || ($name =~ m/^time period$/i) ||
         ($name =~ m/^time periods$/i) || ($name =~ m/^per$/i) ||
         ($name =~ m/^All Periods$/i) || ($name =~ m/^period description/i) ||
         ($name =~ m/^period$/))
  {
    return("time");
  }

  #detect dimension aliases
  elsif ($name =~ m/^palias/i)
  {
    return("palias");
  }
  elsif ($name =~ m/^galias/i)
  {
    return("galias");
  }
  elsif (($name =~ m/^talias/i) || ($name =~ m/^per_alias/i))
  {
    return("talias");
  }

  #detect attributes
  elsif (($name =~ m/^pattr\:(.*)/i) || ($name eq "PRODUCT KEY") ||
      ($name eq "First Wk Selling"))
  {
    return("pattr");
  }
  elsif ($name =~ m/^gattr\:(.*)/i)
  {
    return("gattr");
  }
  elsif ($name =~ m/^tattr\:(.*)/i)
  {
    return("tattr");
  }

  #detect explicit segmentation columns
  elsif ($name =~ m/^pseg\:(.*)/i)
  {
    return("pseg");
  }
  elsif ($name =~ m/^gseg\:(.*)/i)
  {
    return("gseg");
  }
  elsif ($name =~ m/^tseg\:(.*)/i)
  {
    return("tseg");
  }

  $tmp = lc($name);

  #if we're a measure
  if ($measureHintHash{$tmp} == 1)
  {
    return("measure");
  }

  #if we're a known pseg
  if ($psegHintHash{$name} == 1)
  {
    return("pseg");
  }
}



#-------------------------------------------------------------------------
#
# Scan the data in each column of the master data table, and try to guess
# what type of data it contains based on column name and data content
#

sub prep_flow_detect_column_types
{
  my ($masterTable, $masterColTable, $query, $dbOutput, $status, $totalCols);
  my ($colID, $name, $colsProcessed, $pct, $opInfo, $q_opInfo, $colName);
  my ($q_name, $dbOutput1, $runningJobs, $colType, $alterQuery, $state);
  my ($upcFound);
  my (@idxQueries);

  my ($prepDB, $kapDB, $flowID, $jobID, $userID) = @_;


  flow_telemetry($prepDB, $jobID, "Starting column type detection");
  PrepUtils_store_scaled_cloud_load($prepDB, $jobID);

  #check the job's state to make sure it's OK for us to run (should be after
  #the MERGE-DATA operation)
  $query = "SELECT opInfo, state FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($opInfo, $state) = $dbOutput->fetchrow_array;
  if ($state ne "MERGE-DATA")
  {
    flow_telemetry($prepDB, $jobID, "WARNING: detect_column_types called out of sequence: $state");
    return;
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "Detecting column types");

  #if the system is fully loaded, back off and wait for an open process slot
  $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
  while (!$okToRun)
  {
    $query = "UPDATE prep.jobs
        SET PID=$$, opInfo='0|Waiting in processing queue', state='COL-TYPES-WAIT'
        WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_details($prepDB, $jobID, "Waiting in processing queue");

    sleep(120);

    $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #set initial status
  $opInfo = "0|Detecting column types";
  $q_opInfo = $prepDB->quote($opInfo);
  $query = "UPDATE prep.jobs SET PID=$$, opInfo=$q_opInfo, state='COLUMN-TYPES'
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Detecting column types");

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #run through each column in the master table, and figure out its type
  $query = "SELECT ID, name FROM $masterColTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  $totalCols = $status;

  $colsProcessed = 0;
  while (($colID, $name) = $dbOutput->fetchrow_array)
  {

    #update detailed status info
    $pct = ($colsProcessed / $totalCols) * 100;
    $pct = int($pct);
    $opInfo = "$pct|Detecting type of $name";
    $q_opInfo = $prepDB->quote($opInfo);
    $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_details($prepDB, $jobID, "Detecting type of $name");
    PrepUtils_set_job_op_pct($prepDB, $jobID, "$pct");

    $colName = "column_" . $colID;

    $colType = prep_flow_detect_column_type_by_name($name);

    #detect UPC/SKU/EAN column
    if ($colType eq "upc")
    {

      #if we've already found a UPC field, treat this one as a pattr
      if ($upcFound > 0)
      {
        flow_telemetry($prepDB, $jobID, "--- $name contains a product attribute");
        $query = "UPDATE $masterColTable SET type='pattr' WHERE ID=$colID";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
      }

      #we're using this field as our UPC
      else
      {
        $upcFound = 1;
        flow_telemetry($prepDB, $jobID, "--- $name contains UPC data");

        $query = "UPDATE $masterColTable SET type='upc' WHERE ID=$colID";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);

        $query = "CREATE INDEX idx_upc ON $masterTable ($colName)
            ALGORITHM=INPLACE LOCK=EXCLUSIVE";
        push(@idxQueries, $query);
      }
    }

    #detect product column
    elsif ($colType eq "product")
    {
      flow_telemetry($prepDB, $jobID, "--- $name contains products");

      $query = "UPDATE $masterColTable SET type='product' WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }

    #detect geography column
    elsif ($colType eq "geography")
    {
      flow_telemetry($prepDB, $jobID, "--- $name contains geographies");

      $query = "UPDATE $masterColTable SET type='geography' WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);

      $query = "CREATE INDEX idx_geography ON $masterTable ($colName)
          ALGORITHM=INPLACE LOCK=EXCLUSIVE";
      push(@idxQueries, $query);
    }

    #detect time period column
    elsif ($colType eq "time")
    {
      flow_telemetry($prepDB, $jobID, "--- $name contains time periods");

      $query = "UPDATE $masterColTable SET type='time' WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);

      $query = "CREATE INDEX idx_time ON $masterTable ($colName)
          ALGORITHM=INPLACE LOCK=EXCLUSIVE";
      push(@idxQueries, $query);
    }

    #detect dimension aliases
    elsif ($colType eq "palias")
    {
      flow_telemetry($prepDB, $jobID, "--- $name contains a product alias");
      $query = "UPDATE $masterColTable SET type='palias' WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
    elsif ($colType eq "galias")
    {
      flow_telemetry($prepDB, $jobID, "--- $name contains a geography alias");
      $query = "UPDATE $masterColTable SET type='galias' WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
    elsif ($colType eq "talias")
    {
      flow_telemetry($prepDB, $jobID, "--- $name contains a time alias");
      $query = "UPDATE $masterColTable SET type='talias' WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }

    #detect attributes
    elsif ($colType eq "pattr")
    {
      if ($colType =~ m/^pattr:(.*)$/)
      {
        $name = $1;
      }
      flow_telemetry($prepDB, $jobID, "--- $name contains a product attribute");
      $q_name = $prepDB->quote($name);
      $query = "UPDATE $masterColTable SET type='pattr', name=$q_name
          WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
    elsif ($colType eq "gattr")
    {
      if ($colType =~ m/^gattr:(.*)$/)
      {
        $name = $1;
      }
      flow_telemetry($prepDB, $jobID, "--- $name contains a geography attribute");
      $q_name = $prepDB->quote($name);
      $query = "UPDATE $masterColTable SET type='gattr', name=$q_name
          WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
    elsif ($colType eq "tattr")
    {
      if ($colType =~ m/^tattr:(.*)$/)
      {
        $name = $1;
      }
      flow_telemetry($prepDB, $jobID, "--- $name contains a time attribute");
      $q_name = $prepDB->quote($name);
      $query = "UPDATE $masterColTable SET type='tattr', name=$q_name
          WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }

    #detect explicit segmentation columns
    elsif ($colType eq "pseg")
    {
      if ($colType =~ m/^pseg:(.*)$/)
      {
        $name = $1;
      }
      flow_telemetry($prepDB, $jobID, "--- $name contains a product segmentation");
      $q_name = $prepDB->quote($name);
      $query = "UPDATE $masterColTable SET type='pseg', name=$q_name WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
    elsif ($colType eq "gseg")
    {
      if ($colType =~ m/^gseg:(.*)$/)
      {
        $name = $1;
      }
      flow_telemetry($prepDB, $jobID, "--- $name contains a geography segmentation");
      $q_name = $prepDB->quote($name);
      $query = "UPDATE $masterColTable SET type='gseg', name=$q_name WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
    elsif ($colType eq "tseg")
    {
      if ($colType =~ m/^tseg:(.*)$/)
      {
        $name = $1;
      }
      flow_telemetry($prepDB, $jobID, "--- $name contains a time segmentation");
      $q_name = $prepDB->quote($name);
      $query = "UPDATE $masterColTable SET type='tseg', name=$q_name WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }

    #detect measures based on column names of common measures
    elsif ($colType eq "measure")
    {
      flow_telemetry($prepDB, $jobID, "--- $name contains a measure");
      $query = "UPDATE $masterColTable SET type='measure' WHERE ID=$colID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);

      #start building the bulk ALTER query
      if (length($alterQuery) < 1)
      {
        $alterQuery = "ALTER TABLE $masterTable ";
      }

      $alterQuery .= "CHANGE COLUMN $colName $colName DOUBLE DEFAULT NULL, ";
    }

    #if we've made it this far, the column is either a measure or a seg
    else
    {
      $query = "SELECT $colName FROM $masterTable
          WHERE $colName REGEXP '[a-z,A-Z,\/]' LIMIT 1";
      $dbOutput1 = $prepDB->prepare($query);
      $status = $dbOutput1->execute;
      PrepUtils_handle_db_err($prepDB, $status, $query);

      #if it contains text, it's a segmentation
      if ($status > 0)
      {
        flow_telemetry($prepDB, $jobID, "--- $name looks like a product segmentation");
        $query = "UPDATE $masterColTable SET type='pseg' WHERE ID=$colID";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);
      }

      #else it only has numbers, so it must be a measure
      else
      {
        flow_telemetry($prepDB, $jobID, "--- $name looks like a measure");

        $query = "UPDATE $masterColTable SET type='measure' WHERE ID=$colID";
        $status = $prepDB->do($query);
        PrepUtils_handle_db_err($prepDB, $status, $query);

        #start building the bulk ALTER query
        if (length($alterQuery) < 1)
        {
          $alterQuery = "ALTER TABLE $masterTable ";
        }

        $alterQuery .= "CHANGE COLUMN $colName $colName DOUBLE DEFAULT NULL, ";
      }
    }
    $colsProcessed++;
  }

  #run our bulk column type ALTER query, if we need to (we always do)
  if (length($alterQuery) > 1)
  {
    flow_telemetry($prepDB, $jobID, "Changing column types for measures");
    $query = "UPDATE prep.jobs SET opInfo='98|Setting up numerical columns'
        WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_details($prepDB, $jobID, "Setting up numerical columns");
    PrepUtils_set_job_op_pct($prepDB, $jobID, "98");

    $alterQuery .= "LOCK=EXCLUSIVE";
    $status = $prepDB->do($alterQuery);
    PrepUtils_handle_db_err($prepDB, $status, $alterQuery);
  }

  #run our index queries
  #NB: no performance benefit gained by combining these into one big ALTER
  $query = "UPDATE prep.jobs SET opInfo='99|Indexing key fields' WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Indexing key fields");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "99");

  foreach $query (@idxQueries)
  {
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  PrepUtils_set_job_op_speed($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);
  flow_telemetry($prepDB, $jobID, "Done detecting column types");

  $query = "UPDATE prep.jobs SET PID=NULL, opInfo='DONE', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);
}



#-------------------------------------------------------------------------
#
# Apply the validation rules for the specified data flow to the specified
# job, marking any invalid rows as such. Return 0 if validation passed, 1
# if failed.
#

sub prep_flow_validate
{
  my ($masterTable, $masterColTable, $query, $colName, $colID, $dbOutput);
  my ($minVal, $maxVal, $minLength, $maxLength, $matches, $matchOp, $opInfo);
  my ($matchVal, $q_val, $userID, $status, $invalidAction, $runningJobs);
  my ($state);
  my (%colNames, %colIDs);

  my ($prepDB, $kapDB, $flowID, $jobID, $interactive) = @_;


  flow_telemetry($prepDB, $jobID, "Starting data validation");
  PrepUtils_store_scaled_cloud_load($prepDB, $jobID);

  #check the job's state to make sure it's OK for us to run (should be after
  #the RECIPE-APPLY operation)
  $userID = -1;
  if ($interactive != 1)
  {
    $query = "SELECT opInfo, state, userID FROM prep.jobs WHERE ID=$jobID";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($opInfo, $state, $userID) = $dbOutput->fetchrow_array;
    if ($state ne "RECIPE-APPLY")
    {
      flow_telemetry($prepDB, $jobID, "WARNING: validate called out of sequence: $state");
      return;
    }
  }

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "Validating data");

  #if the system is fully loaded, back off and wait for an open process slot
  $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
  while (!$okToRun)
  {
    $query = "UPDATE prep.jobs
        SET PID=$$, opInfo='0|Waiting in processing queue', state='VALIDATE-WAIT'
        WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_details($prepDB, $jobID, "Waiting in processing queue");

    sleep(120);

    $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
  }

  $startTime = PrepUtils_get_current_timestamp($prepDB);

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  $query = "UPDATE prep.jobs
      SET PID=$$, opInfo='0|Validating data', state='VALIDATE'
      WHERE ID=$jobID";
  $prepDB->do($query);

  #get the action we're supposed to take on invalid lines (default to ignore)
  $query = "SELECT invalidAction FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($invalidAction) = $dbOutput->fetchrow_array;

  #get a hash of every column name in the flow for this job
  %colNames = prep_flow_get_column_hash($prepDB, $flowID, $jobID);
  %colIDs = reverse(%colNames);

  #turn off SQL binary logging for performance benefits, since we're not
  #actually replicating this data
  $query = "SET sql_log_bin = 0";
  $prepDB->do($query);
  $query = "SET SESSION transaction_isolation='READ-UNCOMMITTED'";
  $prepDB->do($query);

  #start by marking every row of data as valid
  #NB: it looks weird for the valid value to be 0, but we're looking
  #    forward to storing which validation rule was triggered in this field
  #    for now, a generic value of > 0 means the row is invalid
  if ($interactive == 1)
  {
    $query = "UPDATE prep.jobs SET opInfo='5|Clearing old validation states'
        WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_details($prepDB, $jobID, "Clearing old validation rules");
    PrepUtils_set_job_op_pct($prepDB, $jobID, "5");

    $query = "UPDATE $masterTable SET valid=0";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    $query = "UPDATE prep.jobs SET validation=NULL WHERE ID=$jobID";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  $query = "UPDATE prep.jobs SET opInfo='10|Validating missing data'
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Validating missing data");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "10");

  #do "must be present" validation
  $query = "SELECT name FROM prep.validation
      WHERE flowID=$flowID AND present = 1";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  while (($colName) = $dbOutput->fetchrow_array)
  {

    #the column exists if we have an ID for it
    $colID = $colIDs{$colName};
    if ($colID < 1)
    {
      $query = "UPDATE $masterTable SET valid=1";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  $query = "UPDATE prep.jobs SET opInfo='20|Validating blank fields' WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Validating blank fields");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "20");

  #do "blanks not allowed" validation
  $query = "SELECT name FROM prep.validation WHERE flowID=$flowID AND blank=0";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  while (($colName) = $dbOutput->fetchrow_array)
  {

    #get the ID for the column we're checking for blanks
    $colID = $colIDs{$colName};

    #if the column exists, set any rows with blanks as invalid
    if ($colID > 0)
    {
      $query = "UPDATE $masterTable SET valid=1 WHERE isnull(column_$colID)";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  $query = "UPDATE prep.jobs SET opInfo='30|Validating minimum values'
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Validating minimum values");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "30");

  #do minimum value validation
  $query = "SELECT name, minVal FROM prep.validation
      WHERE flowID=$flowID AND !isnull(minVal)";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($colName, $minVal) = $dbOutput->fetchrow_array)
  {

    #get the ID for the column we're checking the value of
    $colID = $colIDs{$colName};

    #if the column exists, mark as invalid rows with a field that's too small
    if ($colID > 0)
    {
      $query = "UPDATE $masterTable SET valid=1 WHERE column_$colID < $minVal";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  $query = "UPDATE prep.jobs SET opInfo='40|Validating maximum values'
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Validating maximum values");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "40");

  #do maximum value validation
  $query = "SELECT name, maxVal FROM prep.validation
      WHERE flowID=$flowID AND !isnull(maxVal)";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($colName, $maxVal) = $dbOutput->fetchrow_array)
  {

    #get the ID for the column we're checking the value of
    $colID = $colIDs{$colName};

    #if the column exists, mark as invalid rows with a field that's too large
    if ($colID > 0)
    {
      $query = "UPDATE $masterTable SET valid=1 WHERE column_$colID > $maxVal";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  $query = "UPDATE prep.jobs SET opInfo='50|Validating minimum string lengths'
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Validating string lengths");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "50");

  #do minimum string length validation
  $query = "SELECT name, minLength FROM prep.validation
      WHERE flowID=$flowID AND !isnull(minLength)";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($colName, $minLength) = $dbOutput->fetchrow_array)
  {

    #get the ID for the column we're checking the length of
    $colID = $colIDs{$colName};

    #if the column exists, mark invalid rows with a field that's too short
    if ($colID > 0)
    {
      $query = "UPDATE $masterTable SET valid=1
          WHERE LENGTH(column_$colID) < $minLength";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  $query = "UPDATE prep.jobs SET opInfo='60|Validating string lengths'
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Validating maximum string lengths");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "60");

  #do maximum string length validation
  $query = "SELECT name, maxLength FROM prep.validation
      WHERE flowID=$flowID AND !isnull(maxLength)";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  while (($colName, $maxLength) = $dbOutput->fetchrow_array)
  {

    #get the ID for the column we're checking the length of
    $colID = $colIDs{$colName};

    #if the column exists, mark invalid rows with a field that's too long
    if ($colID > 0)
    {
      $query = "UPDATE $masterTable SET valid=1
          WHERE LENGTH(column_$colID) > $maxLength";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  $query = "UPDATE prep.jobs SET opInfo='70|Validating text values' WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Validating text values");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "70");

  #do string matching validation
  $query = "SELECT name, matches FROM prep.validation
      WHERE flowID=$flowID AND !isnull(matches)";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  while (($colName, $matches) = $dbOutput->fetchrow_array)
  {

    #get the ID for the column we're matching against
    $colID = $colIDs{$colName};

    #if the column exists, run matching against it
    if ($colID > 0)
    {

      #extract our match type and match value
      $matches =~ m/^(.*?) (.*)$/;
      $matchOp = $1;
      $matchVal = $2;

      #build up the SQL match value
      if ($matchOp eq "contains")
      {
        $matchVal = "%" . $matchVal . "%";
      }
      elsif ($matchOp eq "starts")
      {
        $matchVal = $matchVal . "%";
      }
      elsif ($matchOp eq "ends")
      {
        $matchVal = "%" . $matchVal;
      }

      $q_val = $prepDB->quote($matchVal);

      $query = "UPDATE $masterTable SET valid=1
          WHERE column_$colID NOT LIKE $q_val";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  $query = "UPDATE prep.jobs SET opInfo='80|Handling invalid data' WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Handling invalid data");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "80");

  #if we're supposed to remove invalid data
  if ($invalidAction eq "remove")
  {
    $query = "DELETE FROM $masterTable WHERE valid=1";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  #elseif we're supposed to warn on invalid data
  elsif ($invalidAction eq "warn")
  {

    #did we actually mark any data as invalid?
    $query = "SELECT * FROM $masterTable WHERE valid=1 LIMIT 1";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);

    if ($status > 0)
    {
      $query = "UPDATE prep.jobs SET validation='ERROR' WHERE ID=$jobID";
      $status = $prepDB->do($query);
      PrepUtils_handle_db_err($prepDB, $status, $query);
    }
  }

  PrepFlows_analyze_job_dim_details($prepDB, $jobID);

  #set state to "loaded" in job history table
  $query = "UPDATE prep.job_history
      SET status='loaded' WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  PrepUtils_set_job_op_speed($prepDB, $jobID, "");

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);

  $query = "UPDATE prep.jobs SET PID=NULL, opInfo='DONE', state='LOADED', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);
}



#-------------------------------------------------------------------------
#
# Export the data from the specified job to a temporary file in a format
# suitable for import to Koala. The name of the export file is based on
# the job ID.
#

sub prep_flow_export_koala
{
  my ($csv, $masterTable, $masterColTable, $query, $filename, $OUTPUT);
  my ($colOrder, $colID, $colName, $type, $subQ, $dbOutput, $totalRows);
  my ($rowsDone, $pct, $opInfo, $q_opInfo, $pid, $childDB, $appendUPC);
  my ($compressWS, $options, $runningJobs, $curPos, $status, $start);
  my ($dimColCount, $maxID, $end);
  my (@orderedCols, @headerCols, @dataVals);

  my ($prepDB, $kapDB, $flowID, $jobID, $userID, $dsID, $createNewDS, $dsName, $dsDescription) = @_;


  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "Exporting data to Koala Analytics");
  PrepUtils_store_scaled_cloud_load($prepDB, $jobID);

  #save exporting state to job history table
  $query = "UPDATE prep.job_history
      SET status='exporting' WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #if the system is fully loaded, back off and wait for an open process slot
  $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
  while (!$okToRun)
  {
    $query = "UPDATE prep.jobs SET PID=$$, opInfo='0|Waiting in processing queue', state='EXP-KOALA-WAIT'
        WHERE ID=$jobID";
    $prepDB->do($query);

    PrepUtils_set_job_op_details($prepDB, $jobID, "Waiting in processing queue");

    sleep(120);

    $okToRun = prep_flow_available_job_slot($prepDB, $kapDB, $jobID, $userID);
  }

  $csv = Text::CSV_XS->new( {binary => 1} );

  #build master table name strings
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  $startTime = PrepUtils_get_current_timestamp($prepDB);
  flow_telemetry($prepDB, $jobID, "Beginning to export data for a Koala data source");

  if (length($dsName) < 1)
  {
    $dsName = ds_id_to_name($kapDB, $dsID);
  }
  $q_dsName = $prepDB->quote($dsName);
  $query = "UPDATE prep.job_history
      SET exportDSID=$dsID, exportDSName=$q_dsName WHERE jobID=$jobID";
  $prepDB->do($query);

  $query = "UPDATE prep.jobs
      SET PID=$$, opInfo='0|Exporting data', state='EXP-KOALA'
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Exporting data");

  PrepFlows_analyze_job_dim_details($prepDB, $jobID);

  #get the number of rows to be exprted
  $query = "SELECT rowCount FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($totalRows) = $dbOutput->fetchrow_array;

  #if the data set is empty, notify the user and finish up
  if ($totalRows < 1)
  {
    flow_telemetry($prepDB, $jobID, "Data set is empty, not exporting to Koala Analytics");

    $query = "UPDATE prep.jobs
        SET PID=NULL, opInfo='ERR|Unable to export to Koala, no data in data set', state='ERROR', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);

    return(-1);
  }

  #save the number of records we're exporting to the job history table
  $query = "UPDATE prep.job_history
      SET recordsExported=$totalRows, status='exported' WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #get IDs of columns in display order
  @orderedCols = prep_flow_order_columns($prepDB, $flowID, $jobID);
  $colOrder = join(',', @orderedCols);

  #get the list of data columns we're exporting
  $query = "SELECT ID, name, type FROM $masterColTable ORDER BY FIELD(ID, $colOrder)";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  undef(@headerCols);
  undef($subQ);
  $dimColCount = 0;
  while (($colID, $colName, $type) = $dbOutput->fetchrow_array)
  {
    $subQ = $subQ . "column_$colID,";

    #use our data tags to explicitly label column types
    if (($type eq "pseg") || ($type eq "gseg") || ($type eq "tseg"))
    {
      $colName = $type . ":" . $colName;
    }
    elsif (($type eq "palias") || ($type eq "galias") || ($type eq "talias"))
    {
      $colName = $type . ":" . $colName;
    }
    elsif (($type eq "pattr") || ($type eq "gattr") || ($type eq "tattr"))
    {
      $colName = $type . ":" . $colName;
    }
    elsif ($type eq "measure")
    {
      $colName = "meas:" . $colName;
    }
    elsif ($type eq "upc")
    {
      $colName = "UPC";
    }
    elsif ($type eq "product")
    {
      $colName = "Product";
      $dimColCount++;
    }
    elsif ($type eq "geography")
    {
      $colName = "Geography";
      $dimColCount++;
    }
    elsif ($type eq "time")
    {
      $colName = "Time";
      $dimColCount++;
    }

    push(@headerCols, $colName);
  }
  chop($subQ);

  if ($dimColCount < 3)
  {
    $query = "UPDATE prep.jobs
        SET PID=$$, opInfo='ERR|Unable to export to Koala, missing dimensions', state='ERROR', lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
    return(-1);
  }

  #open CSV output file that we're going to pass to Koala
  $filename = "/opt/apache/app/tmp/$userID" . ".prep." . $jobID . ".export.csv";
  open(OUTPUT, ">$filename") or die("$!");

  #output the header line to the output CSV file
  $csv->print(OUTPUT, \@headerCols);
  print OUTPUT "\n";

  #get the highest ID number we're going to use for exporting
  $query = "SELECT MAX(ID) FROM $masterTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($maxID) = $dbOutput->fetchrow_array;

  #export the data from the master table, 100K rows at a time
  $rowsDone = 0;
  $start = 0;
  while ($start <= $maxID)
  {

    $end = $start + 100_000;

    #select the data from the data file
    $query = "SELECT $subQ FROM $masterTable WHERE ID > $start && ID <= $end";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);

    #output every line as CSV for import into Koala Analytics
    while (@dataVals = $dbOutput->fetchrow_array)
    {

      if (($rowsDone % 5000) == 0)
      {
        $pct = (($rowsDone / $totalRows) * 100) - 1;
        $pct = int($pct);
        $opInfo = "$pct|Exporting data";
        $q_opInfo = $prepDB->quote($opInfo);
        $query = "UPDATE prep.jobs SET opInfo=$q_opInfo WHERE ID=$jobID";
        $prepDB->do($query);
        $rowsRemaining = $totalRows - $rowsDone;
        $rowsRemaining = prep_autoscale_number($rowsRemaining);
        PrepUtils_set_job_op_extra($prepDB, $jobID, "<B>Records remaining:</B> $rowsRemaining");
        PrepUtils_set_job_op_pct($prepDB, $jobID, $pct);
      }

      $csv->print(OUTPUT, \@dataVals);
      print OUTPUT "\n";
      $rowsDone++;
    }

    $start = $end;
  }

  #close the CSV output file
  close(OUTPUT);

  flow_telemetry($prepDB, $jobID, "Done exporting data for a Koala data source");

  #clear detailed op stats
  PrepUtils_set_job_op_title($prepDB, $jobID, "Loading data into Koala data source");

  flow_telemetry($prepDB, $jobID, "Starting update of Koala data source");
  $query = "UPDATE prep.jobs
      SET opInfo='0|Loading Koala data source', state='UPDATE-KOALA', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);

  PrepUtils_set_job_op_details($prepDB, $jobID, "Starting update of Koala data source");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "0");

  #grab all of the data flow-specific settings for updating the data source
  $query = "SELECT appendUPC, compressWS, dontOverwriteNames, pmatch, gmatch, tmatch
      FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($appendUPC, $compressWS, $dontOverwriteNames, $pmatch, $gmatch, $tmatch) = $dbOutput->fetchrow_array;

  $options = "";
  if ($appendUPC > 0)
  {
    $options .= "appendUPC,";
  }
  if ($compressWS > 0)
  {
    $options .= "compressWS";
  }

  if (($pmatch eq "auto") || (length($pmatch) < 1))
  {
    $pmatch = "upc";
  }
  if (($gmatch eq "auto") || (length($gmatch) < 1))
  {
    $gmatch = "name";
  }
  if (($tmatch eq "auto") || (length($tmatch) < 1))
  {
    $tmatch = "name";
  }

  #redirect error output to a per-datasource file
  close(STDERR);
  close(STDOUT);
  open(STDERR, ">>/opt/apache/htdocs/tmp/dsimport_$dsID.log") or die("Unable to open STDERR, $!");
  open(STDOUT, ">>/opt/apache/htdocs/tmp/dsimport_$dsID.log") or die ("Unable to open STDOUT, $!");

  select(STDERR);
  $| = 1;

  ds_create_update($kapDB, $userID, $dsID, $createNewDS, "Tabular", $pmatch, $gmatch, $tmatch, "prep.$jobID", "", $options, $dontOverwriteNames, "");

  #clear detailed op stats
  if (!($prepDB->ping))
  {
    $prepDB = PrepUtils_connect_to_database();
  }

  PrepUtils_increment_job_run_time($prepDB, $jobID, $startTime);

  PrepUtils_set_job_op_title($prepDB, $jobID, "");
  PrepUtils_set_job_op_details($prepDB, $jobID, "");
  PrepUtils_set_job_op_extra($prepDB, $jobID, "");
  PrepUtils_set_job_op_pct($prepDB, $jobID, "");
  PrepUtils_set_job_op_speed($prepDB, $jobID, "");
}



#-------------------------------------------------------------------------
#
# Run the specified data flow (usually called by scheduler)
#

sub prep_run_flow
{
  my ($query, $dbOutput, $status, $type, $validationStatus, $invalidAction);
  my ($dsID);

  my ($prepDB, $kapDB, $userID, $flowID, $jobID) = @_;


  #grab the initial info we need to start running the flow
  $query = "SELECT source FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($type) = $dbOutput->fetchrow_array;

  #store our PID
  $query = "UPDATE prep.jobs SET PID=$$ WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);


  #------------------------------
  #
  # source*.cld operations
  #

  #get the raw source data using the selected method
  if ($type eq "Web")
  {
    $query = "UPDATE prep.jobs SET state='LOAD-WEB' WHERE ID=$jobID";
    $prepDB->do($query);
    prep_source_web($prepDB, $jobID, $jobID);
  }
  elsif ($type eq "FTP")
  {
    $query = "UPDATE prep.jobs SET state='LOAD-FTP' WHERE ID=$jobID";
    $prepDB->do($query);
    prep_source_ftp($prepDB, $jobID, $jobID);
  }
  elsif ($type eq "Database")
  {
    $query = "UPDATE prep.jobs SET state='LOAD-DB' WHERE ID=$jobID";
    $prepDB->do($query);
    prep_source_database($prepDB, $jobID, $jobID);
  }
  elsif ($type eq "AmazonS3")
  {
    $query = "UPDATE prep.jobs SET state='LOAD-AMAZON' WHERE ID=$jobID";
    $prepDB->do($query);
    prep_source_amazon($prepDB, $jobID, $jobID);
  }
  elsif ($type eq "Koala")
  {
    $query = "UPDATE prep.jobs SET state='LOAD-KOALA' WHERE ID=$jobID";
    $prepDB->do($query);
    prep_source_koala($kapDB, $prepDB, $jobID);
  }
  elsif ($type eq "Manual")
  {
    #nothing for us to do here
  }
  elsif ($type eq "Paste")
  {
    #nothing for us to do here
  }
  else
  {
    #ERROR
    return(0);
  }

  #see if there was an error condition - exit out if so
  $query = "SELECT state FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $dbOutput->execute;
  ($state) = $dbOutput->fetchrow_array;
  if ($state eq "ERROR")
  {
    $query = "UPDATE prep.jobs
        SET PID=NULL, mode='interactive', runProgress=NULL, lastAction=NOW()
        WHERE ID=$jobID";
    $prepDB->do($query);
    return(-1);
  }


  #---------------------------------------------------------------------
  #
  # flowExtractData.cld
  #

  $query = "UPDATE prep.jobs
      SET PID=$$, runProgress='10|EXTRACT', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);

  #expand any zip archives that might have been included in the data upload
  prep_flow_expand_zips($prepDB, $jobID, $userID, $jobID);

  #run through every file in the upload director, looking for files
  #uploaded by our user that need to have data extracted, then add all of
  #the files to the prep.files table
  if (!($kapDB->ping))
  {
    $kapDB = KAPutil_connect_to_database();
  }

  prep_flow_extract_file_data($prepDB, $kapDB, $flowID, $jobID, $userID, $jobID);


  #---------------------------------------------------------------------
  #
  # flowDetectNested.cld
  #

  $query = "UPDATE prep.jobs SET PID=$$, runProgress='20|NESTED', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);

  if (!($kapDB->ping))
  {
    $kapDB = KAPutil_connect_to_database();
  }

  prep_flow_nested_to_tabular($prepDB, $kapDB, $flowID, $jobID, $userID);


  #---------------------------------------------------------------------
  #
  # flowLoadRawData.cld
  #

  $query = "UPDATE prep.jobs SET PID=$$, runProgress='30|LOAD', state='PARSE-WAIT', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);

  if (!($kapDB->ping))
  {
    $kapDB = KAPutil_connect_to_database();
  }

  prep_flow_load_raw_data($prepDB, $kapDB, $flowID, $jobID, $userID);


  #---------------------------------------------------------------------
  #
  # flowDataType.cld
  #

  $query = "UPDATE prep.jobs
      SET PID=$$, runProgress='40|FILETYPE', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);

  prep_flow_set_file_types($prepDB, $flowID, $jobID);


  #---------------------------------------------------------------------
  #
  # flowMergeData.cld
  #

  $query = "UPDATE prep.jobs
      SET PID=$$, runProgress='50|MERGE', state='DATATYPE-WAIT', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);

  if (!($kapDB->ping))
  {
    $kapDB = KAPutil_connect_to_database();
  }

  prep_flow_merge_data($prepDB, $kapDB, $flowID, $jobID, $userID);


  #---------------------------------------------------------------------
  #
  # flowColumnTypes.cld
  #

  $query = "UPDATE prep.jobs
      SET PID=$$, runProgress='60|COLTYPE', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);

  if (!($kapDB->ping))
  {
    $kapDB = KAPutil_connect_to_database();
  }

  prep_flow_detect_column_types($prepDB, $kapDB, $flowID, $jobID, $userID);


  #---------------------------------------------------------------------
  #
  # flowApplyRecipe.cld
  #

  $query = "UPDATE prep.jobs
      SET PID=$$, runProgress='70|RECIPE', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);

  if (!($kapDB->ping))
  {
    $kapDB = KAPutil_connect_to_database();
  }

  Lib::PrepRecipes::prep_recipe_apply($prepDB, $kapDB, $flowID, $jobID);


  #---------------------------------------------------------------------
  #
  # validateApply.cld
  #

  $query = "UPDATE prep.jobs
      SET PID=$$, runProgress='80|VALIDATE', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);

  if (!($kapDB->ping))
  {
    $kapDB = KAPutil_connect_to_database();
  }

  prep_flow_validate($prepDB, $kapDB, $flowID, $jobID, 0);

  #if validation failed and we're supposed to warn the user
  $query = "SELECT validation FROM prep.jobs WHERE ID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($validationStatus) = $dbOutput->fetchrow_array;

  if ($validationStatus eq "ERROR")
  {
    $query = "SELECT invalidAction FROM prep.flows WHERE ID=$flowID";
    $dbOutput = $prepDB->prepare($query);
    $status = $dbOutput->execute;
    PrepUtils_handle_db_err($prepDB, $status, $query);
    ($invalidAction) = $dbOutput->fetchrow_array;

    if ($invalidAction eq "warn")
    {
      $query = "UPDATE prep.jobs
          SET PID=NULL, opInfo='DONE-RUN', lastAction=NOW(), state='LOADED', runProgress='LOADED', mode='interactive'
          WHERE ID=$jobID";
      $prepDB->do($query);
      return;
    }
  }


  #---------------------------------------------------------------------
  #
  # exportKoalaDS.cld
  #

  $query = "UPDATE prep.jobs
      SET PID=$$, runProgress='90|EXPORT-KOALA', lastAction=NOW()
      WHERE ID=$jobID";
  $prepDB->do($query);

  #if there's a Koala DS specified, update it with the data
  $query = "SELECT dsID FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($dsID) = $dbOutput->fetchrow_array;

  if (!($kapDB->ping))
  {
    $kapDB = KAPutil_connect_to_database();
  }

  if ($dsID > 0)
  {
    $status = prep_flow_export_koala($prepDB, $kapDB, $flowID, $jobID, $userID, $dsID, 0);
    if ($status == -1)
    {
      $query = "UPDATE prep.jobs
          SET PID=NULL, lastAction=NOW(), runProgress=NULL, mode='interactive'
          WHERE ID=$jobID";
      $prepDB->do($query);
      return;
    }
  }

  $query = "UPDATE prep.jobs
      SET PID=NULL, opInfo='DONE-RUN', lastAction=NOW(), state='LOADED', runProgress='LOADED', mode='interactive'
      WHERE ID=$jobID";
  $prepDB->do($query);
}



#-------------------------------------------------------------------------
#
# Analyze the dimension info (P/G/T/M count and distinct values) for a job, and
# store the results in the the job_history table. Should be called once as part
# of validation process, and then again when a job is exported to a data source.
#

sub PrepFlows_analyze_job_dim_details
{
  my ($query, $dbOutput, $status, $csv, $masterTable, $masterColTable);
  my ($prodColumnID, $prodColumnName, $csvLine);
  my (@prodSamples);

  my ($prepDB, $jobID) = @_;


  $csv = Text::CSV_XS->new( {binary => 1} );

  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  #if we've already calculated valid values for all columns, don't do it again
  $query = "SELECT (numProducts * numGeos * numTimes * numMeasures)
      FROM prep.job_history WHERE jobID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute();
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($totalTuples) = $dbOutput->fetchrow_array;
  if ($totalTuples > 0)
  {
    return;
  }

  #determine which column contains product info
  #TODO: if it exists, use UPC column in place of product name
  $query = "SELECT ID FROM $masterColTable WHERE type='product'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute();
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($prodColumnID) = $dbOutput->fetchrow_array;
  $prodColumnName = "column_" . $prodColumnID;

  $query = "SELECT DISTINCT $prodColumnName FROM $masterTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $prodCount = $dbOutput->execute();
  PrepUtils_handle_db_err($prepDB, $status, $query);

  $count = 1;
  while ((($prodName) = $dbOutput->fetchrow_array) && ($count <= 50))
  {
    push(@prodSamples, $prodName);
    $count++;
  }
  $csv->combine(@prodSamples);
  $csvLine = $csv->string();
  $csvLine = $prepDB->quote($csvLine);

  $query = "UPDATE prep.job_history
      SET numProducts=$prodCount, productNames=$csvLine
      WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #determine which column contains geography info
  $query = "SELECT ID FROM $masterColTable WHERE type='geography'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute();
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($geoColumnID) = $dbOutput->fetchrow_array;
  $geoColumnName = "column_" . $geoColumnID;

  $query = "SELECT DISTINCT $geoColumnName FROM $masterTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $geoCount = $dbOutput->execute();
  PrepUtils_handle_db_err($prepDB, $status, $query);

  $count = 1;
  while ((($geoName) = $dbOutput->fetchrow_array) && ($count <= 50))
  {
    push(@geoSamples, $geoName);
    $count++;
  }
  $csv->combine(@geoSamples);
  $csvLine = $csv->string();
  $csvLine = $prepDB->quote($csvLine);

  $query = "UPDATE prep.job_history
      SET numGeos=$geoCount, geoNames=$csvLine
      WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #determine which column contains time period info
  $query = "SELECT ID FROM $masterColTable WHERE type='time'";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute();
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($timeColumnID) = $dbOutput->fetchrow_array;
  $timeColumnName = "column_" . $timeColumnID;

  $query = "SELECT DISTINCT $timeColumnName FROM $masterTable";
  $dbOutput = $prepDB->prepare($query);
  $status = $timeCount = $dbOutput->execute();
  PrepUtils_handle_db_err($prepDB, $status, $query);

  $count = 1;
  while ((($timeName) = $dbOutput->fetchrow_array) && ($count <= 50))
  {
    push(@timeSamples, $timeName);
    $count++;
  }
  $csv->combine(@timeSamples);
  $csvLine = $csv->string();
  $csvLine = $prepDB->quote($csvLine);

  $query = "UPDATE prep.job_history
      SET numTimes=$timeCount, timeNames=$csvLine
      WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #get the number and names of measures in the job
  $query = "SELECT name FROM $masterColTable WHERE type='measure'";
  $dbOutput = $prepDB->prepare($query);
  $status = $measCount = $dbOutput->execute();
  PrepUtils_handle_db_err($prepDB, $status, $query);

  $count = 1;
  while ((($measName) = $dbOutput->fetchrow_array) && ($count <= 50))
  {
    push(@measSamples, $measName);
    $count++;
  }
  $csv->combine(@measSamples);
  $csvLine = $csv->string();
  $csvLine = $prepDB->quote($csvLine);

  $query = "UPDATE prep.job_history
      SET numMeasures=$measCount, measureNames=$csvLine
      WHERE jobID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Update the source file size and date for scheduling purposes
#

sub prep_flow_update_schedule_status
{
  my ($query, $dbOutput, $fileSize, $fileDate, $status, $source);
  my ($masterTable, $masterColTable);

  my ($prepDB, $flowID) = @_;


  #make sure the dbh is still active, just in case it got lost during update
  $status = $prepDB->ping();
  if ($status < 1)
  {
    $prepDB = PrepUtils_connect_to_database();
  }

  $query = "SELECT source FROM prep.flows WHERE ID=$flowID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);
  ($source) = $dbOutput->fetchrow_array;

  if ($source eq "Web")
  {
    ($fileSize, $fileDate) = prep_source_web_newdata($prepDB, $flowID);
  }
  elsif ($source eq "FTP")
  {
    ($fileSize, $fileDate) = prep_source_ftp_newdata($prepDB, $flowID);
  }
  elsif ($source eq "Koala")
  {
    ($fileSize, $fileDate) = prep_source_koala_newdata($kapDB, $prepDB, $flowID);
  }

  #handle error conditions (fileSize and fileDate will be undef)
  if (!defined($fileSize))
  {
    $fileSize = 0;
    $fileDate = '1990-01-01 00:00:00';
  }

  #update the lastRun in schedule
  $query = "UPDATE prep.schedule \
      SET lastRun=NOW(), lastFileSize=$fileSize, lastFileDate='$fileDate' \
      WHERE flowID=$flowID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Clear the specified job (remove the job and all of its associated data files)
#

sub prep_job_clear
{
  my ($query, $dbOutput, $fileID, $tabID, $dataTable, $colTable, $status);
  my ($masterTable, $masterColTable);

  my ($prepDB, $flowID, $jobID) = @_;


  #remove the job from the jobs table
  $query = "DELETE FROM prep.jobs WHERE ID=$jobID";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #grab the list of every data table the job uses
  $query = "SELECT ID, tabID FROM prep.files WHERE jobID=$jobID";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #delete the data file and associated column table
  while (($fileID, $tabID) = $dbOutput->fetchrow_array)
  {
    $dataTable = $jobID . "_" . $fileID . "_" . $tabID;
    $colTable = $jobID . "_" . $fileID . "_" . $tabID . "_cols";

    $query = "DROP TABLE prep_data.$dataTable";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);

    $query = "DROP TABLE prep_data.$colTable";
    $status = $prepDB->do($query);
    PrepUtils_handle_db_err($prepDB, $status, $query);
  }

  $query = "DELETE FROM prep.files WHERE jobID=$jobID";
  $prepDB->do($query);

  #delete master data and column tables
  $masterTable = "prep_data.$jobID" . "_master";
  $masterColTable = "prep_data.$jobID" . "_master_cols";

  $query = "DROP TABLE $masterTable";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);

  $query = "DROP TABLE $masterColTable";
  $status = $prepDB->do($query);
  PrepUtils_handle_db_err($prepDB, $status, $query);
}



#-------------------------------------------------------------------------
#
# Return a count of currently running jobs (used to prevent the customer's
# data prep cloud from becoming saturated).
#

sub prep_running_jobs
{
  my ($query, $dbOutput, $status, $opInfo, $state, $runningJobs);

  my ($prepDB) = @_;


  #get a list of all running jobs on the cloud
  $query = "SELECT mode, opInfo, state, runProgress FROM prep.jobs
      WHERE state NOT IN ('LOADED', 'ERROR')";
  $dbOutput = $prepDB->prepare($query);
  $status = $dbOutput->execute;
  PrepUtils_handle_db_err($prepDB, $status, $query);

  #run through the list of jobs, and determine which are currently using
  #a cpu
  $runningJobs = 0;
  while (($mode, $opInfo, $state, $runProgress) = $dbOutput->fetchrow_array)
  {

    if (($opInfo =~ m/^DONE/) && ($state eq "LOADED"))
    {
      next;
    }

    #jobs "waiting" aren't "running"
    if ($state =~ m/WAIT/)
    {
      next;
    }

    $runningJobs++;
  }

  return($runningJobs);
}



#-------------------------------------------------------------------------
#
# "Autoscale" a number to make readability easier for end users.
#

sub prep_autoscale_number
{
  my ($val) = @_;


  #handle billions
  if ($val > 1_000_000_000)
  {
    $val = $val / 1_000_000_000;
    $val = sprintf("%.1f", $val);
    $val = "$val" . "B";
  }
  elsif ($val > 1_000_000)
  {
    $val = $val / 1_000_000;
    $val = sprintf("%.1f", $val);
    $val = "$val" . "M";
  }
  elsif ($val > 1000)
  {
    $val = $val / 1000;
    $val = sprintf("%.1f", $val);
    $val = "$val" . "K";
  }

  #knock .0 off end of value
  if ($val =~ m/^(.*)\.0(.)$/)
  {
    $val = $1 . $2;
  }

  return($val);
}



#-------------------------------------------------------------------------
#
# Return a text block containing the HTML/JS for the
# Sets the "title" of an operation (displayed at top of status dialogs in UI).
#

sub prep_web_cloud_dashboard
{
  my ($query, $dbOutput, $jobWarnThreshold, $overuseThreshold);
  my ($activeJobs, $cpu, $storage, $storageThroughput, $memory);
  my ($storageColor, $html);

  my ($kapDB, $prepDB) = @_;


  #get initial performance monitoring info
  $query = "SELECT jobs, cpu, storage, storageThroughput, memory
      FROM app.performance WHERE instance='prep'";
  $dbOutput = $kapDB->prepare($query);
  $dbOutput->execute;
  ($activeJobs, $cpu, $storage, $storageThroughput, $memory) = $dbOutput->fetchrow_array;
  $jobWarnThreshold = $Lib::KoalaConfig::prepCores * 0.75;
  $overuseThreshold = $Lib::KoalaConfig::prepCores + 1;

  if ($storage >= 90)
  {
    $storageColor = "#f2726f";
  }
  elsif ($storage >= 75)
  {
    $storageColor = "#FFC533";
  }
  else
  {
    $storageColor = "#62B58F";
  }

  $html = <<END_HTML;
  <SCRIPT>
  FusionCharts.ready(function()
  {
    let jobsBulb = new FusionCharts(
    {
      type: "bulb",
      id: "gauge-jobs-bulb",
      renderAt: "div-gauge-jobs-bulb",
      width: "150",
      height: "175",
      dataFormat: "json",
      dataSource:
      {
        chart:
        {
          caption: "Jobs",
          upperlimit: "$Lib::KoalaConfig::prepCores",
          lowerlimit: "0",
          numberSuffix: " jobs",
          useColorNameAsValue: "1",
          placeValuesInside: "1",
          plottooltext: "Active Jobs: <b>\$dataValue</b>",
          theme: "fusion"
        },
        colorrange:
        {
          color: [
          {
            minvalue: "0",
            maxvalue: "$jobWarnThreshold",
            label: "Normal",
            code: "#62B58F"
          },
          {
            minvalue: "$jobWarnThreshold",
            maxvalue: "$Lib::KoalaConfig::prepCores",
            label: "Heavy Usage",
            code: "#FFC533"
          },
          {
            minvalue: "$overuseThreshold",
            maxvalue: "100",
            label: "Over Used",
            code: "#F2726F"
          }]
        },

        value: "$activeJobs"
      },
      events:
      {
        rendered: function(evtObj, argObj)
        {
          let chartRef = evtObj.sender;
          chartRef.intervalUpdateId = setInterval(function()
          {
            \$.get('/app/prep/ajaxAPI.cld?svc=perf_jobs', function(data, status)
            {
              chartRef.feedData(data);
            });
          }, 60000);
        },
        disposed: function(evt, args)
        {
          clearInterval(evt.sender.intervalUpdateId);
        }
      }
    }).render();

    let cpuAngular = new FusionCharts(
    {
      type: "angulargauge",
      id: "gauge-cpu-angular",
      renderAt: "div-gauge-cpu-angular",
      width: "150",
      height: "175",
      dataFormat: "json",
      dataSource:
      {
        chart:
        {
          caption: "CPU",
          upperlimit: "100",
          lowerlimit: "0",
          numberSuffix: "%",
          showValue: "1",
          showTickValues: "0",
          plottooltext: "\$dataValue",
          theme: "fusion"
        },
        colorrange:
        {
          color: [
          {
            minvalue: "0",
            maxvalue: "75",
            code: "#62B58F"
          },
          {
            minvalue: "75",
            maxvalue: "90",
            code: "#FFC533"
          },
          {
            minvalue: "90",
            maxvalue: "100",
            code: "#F2726F"
          }]
        },
        "dials":
        {
          "dial": [
          {
            "value": "$cpu"
          }]
        }
      },
      events:
      {
        rendered: function(evtObj, argObj)
        {
          let chartRef = evtObj.sender;
          chartRef.intervalUpdateId = setInterval(function()
          {
            \$.get('/app/prep/ajaxAPI.cld?svc=perf_cpu', function(data, status)
            {
              chartRef.feedData(data);
            });
          }, 60000);
        },
        disposed: function(evt, args)
        {
          clearInterval(evt.sender.intervalUpdateId);
        }
      }
    }).render();

    let memoryAngular = new FusionCharts(
    {
      type: "angulargauge",
      id: "gauge-memory-angular",
      renderAt: "div-gauge-memory-angular",
      width: "150",
      height: "175",
      dataFormat: "json",
      dataSource:
      {
        chart:
        {
          caption: "Memory",
          upperlimit: "100",
          lowerlimit: "0",
          numberSuffix: "%",
          showValue: "1",
          showTickValues: "0",
          plottooltext: "\$dataValue",
          theme: "fusion"
        },
        colorrange:
        {
          color: [
          {
            minvalue: "0",
            maxvalue: "90",
            code: "#62B58F"
          },
          {
            minvalue: "95",
            maxvalue: "100",
            code: "#F2726F"
          }]
        },
        "dials":
        {
          "dial": [
          {
            "value": "$memory"
          }]
        }
      },
      events:
      {
        rendered: function(evtObj, argObj)
        {
          let chartRef = evtObj.sender;
          chartRef.intervalUpdateId = setInterval(function()
          {
            \$.get('/app/prep/ajaxAPI.cld?svc=perf_memory', function(data, status)
            {
              chartRef.feedData(data);
            });
          }, 60000);
        },
        disposed: function(evt, args)
        {
          clearInterval(evt.sender.intervalUpdateId);
        }
      }
    }).render();

    let throughputAngular = new FusionCharts(
    {
      type: "angulargauge",
      id: "gauge-throughput-angular",
      renderAt: "div-gauge-throughput-angular",
      width: "150",
      height: "175",
      dataFormat: "json",
      dataSource:
      {
        chart:
        {
          caption: "Throughput",
          upperlimit: "100",
          lowerlimit: "0",
          numberSuffix: "%",
          showValue: "1",
          showTickValues: "0",
          plottooltext: "\$dataValue",
          theme: "fusion"
        },
        colorrange:
        {
          color: [
          {
            minvalue: "0",
            maxvalue: "75",
            code: "#62B58F"
          },
          {
            minvalue: "75",
            maxvalue: "90",
            code: "#FFC533"
          },
          {
            minvalue: "90",
            maxvalue: "100",
            code: "#F2726F"
          }]
        },
        "dials":
        {
          "dial": [
          {
            "value": "$storageThroughput"
          }]
        }
      },
      events:
      {
        rendered: function(evtObj, argObj)
        {
          let chartRef = evtObj.sender;
          chartRef.intervalUpdateId = setInterval(function()
          {
            \$.get('/app/prep/ajaxAPI.cld?svc=perf_throughput', function(data, status)
            {
              chartRef.feedData(data);
            });
          }, 60000);
        },
        disposed: function(evt, args)
        {
          clearInterval(evt.sender.intervalUpdateId);
        }
      }
    }).render();

    let storageCylinder = new FusionCharts(
    {
      type: "cylinder",
      id: "gauge-storage-cylinder",
      renderAt: "div-gauge-storage-cylinder",
      width: "150",
      height: "175",
      dataFormat: "json",
      dataSource:
      {
        chart:
        {
          caption: "Storage",
          upperlimit: "100",
          lowerlimit: "0",
          numberSuffix: "%",
          showTickMarks: "0",
          showValue: "1",
          theme: "fusion",
          cylFillColor: "$storageColor",
        },
        value: "$storage"
      },
      events:
      {
        rendered: function(evtObj, argObj)
        {
          let chartRef = evtObj.sender;
          chartRef.intervalUpdateId = setInterval(function()
          {
            \$.get('/app/prep/ajaxAPI.cld?svc=perf_storage', function(data, status)
            {
              chartRef.feedData(data);
            });
          }, 60000);
        },
        disposed: function(evt, args)
        {
          clearInterval(evt.sender.intervalUpdateId);
        }
      }
    }).render();

  });
  </SCRIPT>
END_HTML

  return($html);
}



#-------------------------------------------------------------------------


1;
